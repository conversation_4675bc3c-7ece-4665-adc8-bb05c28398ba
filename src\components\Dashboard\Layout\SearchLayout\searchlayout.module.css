.search {
  width: 70px;
  padding: 20px 0 0 0;
  box-shadow: -5px 0px 15px 5px #0000000d;
  height: 100vh;
  position: fixed;
  right: 0;
  top: 76px;
  transition: width 0.3s ease;
  background-color: white;
}

.searchOpened {
  width: 290px !important;
  padding: 65px 28px !important;
}

.searchInput {
  outline: 0;
  border-bottom: 1px solid #666666;
  color: #666666;
}

.contractExecutor {
  margin: 10px 0 !important;
}

@media screen and (min-width: 600px) {
  .search {
    width: 70px;
    top: 100px;
    padding: 20px 0 0 0;
  }
  
  .searchOpened {
    width: 290px !important;
    padding: 65px 28px !important;
  }
}