import inputstyleclasses from "../inputstyle.module.css"

function ResetPass() {
  return (
    <div className="w-full flex flex-col">
      <form className="w-full">
        <div className="w-full mt-[42px] mb-[34px]">
          <p className={inputstyleclasses.mailSentMsg}><EMAIL> hesabına yeniləmə keçidi
            göndəriləcək.</p>
          <p className={`${inputstyleclasses.inputStyle}`}><input className={`w-full`} placeholder="E-mail daxil edin" type="mail" /></p>
        </div>
        <button className="defButton bgGreen mx-auto block mb-[18px]">Daxil ol</button>
      </form>
    </div>
  )
}

export default ResetPass