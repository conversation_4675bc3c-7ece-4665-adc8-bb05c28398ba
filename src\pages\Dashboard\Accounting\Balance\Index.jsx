import { useSelector } from "react-redux";
import { Helmet } from "react-helmet-async";
import { useGetAllBalanceQuery } from "@/redux/services/accountingApiSlice";
import { formatBalanceData } from "@/util/formatters";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";

import TableContainer from "@/components/Dashboard/Accounting/Balance/Table/TableContainer";
import TableBody from "@/components/Dashboard/Accounting/Balance/Table/TableBody";
import ActionModal from "@/components/Dashboard/Accounting/Balance/ActionModal/ActionModal";

function BalancePage() {
    const { data: balance, isLoading, isError, error } = useGetAllBalanceQuery();
    const { isModalOpen } = useSelector(state => state.balanceModal);

    if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

    const tableData = !isLoading && formatBalanceData(balance);

    return (
        <>
            <Helmet>
                <title>inKredo | Balans</title>
            </Helmet>
            <div className="pb-8 pt-[30px] w-full flex gap-40 formElement sm:pr-8">
                {isLoading ? (
                    <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
                        <ContentLoadingScreen />
                    </div>
                ) : (
                    balance && (
                        <>
                            <ActionModal isOpen={isModalOpen} />
                            <TableContainer label={"Aktivlər"} sum={balance.assets.sum}>
                                <TableBody data={tableData.assetsData} />
                            </TableContainer>
                            <TableContainer label={"Passivlər"} sum={balance.liabilities.sum}>
                                <TableBody data={tableData.liabilityData} />
                            </TableContainer>
                        </>
                    )
                )}
            </div>
        </>
    )
}

export default BalancePage
