import SearchComponent from "@/components/shared/Search/SearchComponent";
import { operationTypes } from "@/util/constants";

export default function OperationSearch() {
  const searchFields = [
    {
      type: 'select',
      name: 'operation_types',
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON>t Növü',
      options: operationTypes,
      fieldProps: { className: 'w-full mt-[30px]' }
    },
    {
      type: 'text',
      name: 'executor',
      label: 'İcraçı',
      fieldProps: { className: 'mb-[10px]' }
    },
    {
      type: 'date',
      name: 'date',
      label: 'Tarix',
      fieldProps: { className: 'mt-[18px] mb-8' }
    }
  ];

  const defaultValues = {
    operation_types: '',
    operation_types_display: { name: "<PERSON><PERSON><PERSON><PERSON>", value: "" },
    executor: '',
    date: [null, null]
  };

  return (
    <SearchComponent
      fields={searchFields}
      defaultValues={defaultValues}
      limit="13"
      formProps={{ className: "mt-[30px]" }}
    />
  );
}
