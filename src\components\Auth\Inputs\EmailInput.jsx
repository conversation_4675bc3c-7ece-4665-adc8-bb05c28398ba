import inputstyleclasses from "@/pages/Auth/inputstyle.module.css"

function EmailInput({register, errors = {}, disabled = false}) {
    return (
        <p className={`${inputstyleclasses.inputStyle}`}>
            <input
                className="w-full"
                placeholder="E-mail hesabı"
                type="email"
                disabled = {disabled}
                {...(register ? register("email") : {})}
            />
            {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.email?.message}</span>}
        </p>
    )
}

export default EmailInput