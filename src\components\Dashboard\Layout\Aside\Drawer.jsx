import inkredosvg from "@/assets/inkredo.svg";
import openCloseLogo from "@/assets/openCloseLogo.svg";

import { Link } from "react-router-dom";
import { drawerNavConfig} from "@/util/drawerNavConfig";
import { useDispatch, useSelector } from "react-redux";
import { handleDrawerToggle, handleDrawerWidth } from "@/redux/features/layout/layoutSlice";
import { DRAWER_WIDTHS } from "@/util/constants";
import CustomNavLink from "./CustomNavLink";

function Drawer() {
    const { drawerWidth, mobileOpen } = useSelector(state => state.layout);
    const dispatch = useDispatch();

    const { COLLAPSED_WIDTH } = DRAWER_WIDTHS;
    const isCollapsed = drawerWidth === COLLAPSED_WIDTH;

    return (
        <div className="flex flex-col justify-between bg-[#073763] h-full">
            <div>
                <div className={`flex justify-between ${isCollapsed && "flex-col gap-5"} items-center min-h-[64px] p-5`}>
                    {!isCollapsed && (
                        <Link to="/">
                            <img className="h-10" src={inkredosvg} alt="inkredo logo" />
                        </Link>
                    )}
                    <button
                        onClick={mobileOpen ? () => dispatch(handleDrawerToggle()) : () => dispatch(handleDrawerWidth())}
                        className={`${isCollapsed ? "rotate-180" : ""} transition-all duration-300`}
                    >
                        <img src={openCloseLogo} alt="open/close drawer" />
                    </button>
                </div>

                <div className="flex flex-col">
                    {drawerNavConfig.map((item, index) => (
                        <CustomNavLink 
                            key={index}
                            isCollapsed={isCollapsed}
                            item={item}
                        />
                    ))}
                </div>
            </div>

            {!isCollapsed && (
                <div className="copyright overflow-hidden whitespace-nowrap text-white p-5 flex items-center justify-center gap-4">
                    <a href="https://kodaze.com/" target="_blank" rel="noopener noreferrer">
                        © Kodaze
                    </a>
                    v 1.1.10
                </div>
            )}
        </div>
    );
}

export default Drawer;