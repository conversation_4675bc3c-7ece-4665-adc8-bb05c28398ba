import { Link, useNavigate } from "react-router-dom"
import inputstyleclasses from "../inputstyle.module.css"
import { useLoginMutation } from "@/redux/services/publicApiSlice";
import { useDispatch } from "react-redux";
import { setCredentials } from "@/redux/features/authSlice";

import { loginSchema as schema } from "@/util/schemas/authSchemas";

import UsernameInput from "@/components/Auth/Inputs/UsernameInput";
import PasswordInput from "@/components/Auth/Inputs/PasswordInput";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { Helmet } from "react-helmet-async";
// import { userApiSlice } from "@/redux/services/userApiSlice";

function Login() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const [login, { isLoading, error }] = useLoginMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
    mode: "onSubmit",
  });

  async function onSubmit(data) {
    const updatedData = {
      ...data,
      username: data.username.toLowerCase()
    }

    try {
      const response = await login(updatedData).unwrap();
      console.log(response);

      dispatch(setCredentials({
        access: response.access,
        refresh: response.refresh,
      }));
      // dispatch(userApiSlice.util.invalidateTags(["Profile"]));

      navigate("/");
    } catch (err) {
      console.error("Login failed:", err);
    }
  }

  const getErrorMessage = () => {
    if (!error) return "";

    if (error.status === 401 || error.status === 400) {
      return "İstifadəçi adı və ya şifrə yanlışdır";
    } else if (error.status === 429) {
      return error.data.detail;
    } else {
      return `Bir xəta baş verdi, xahiş edirik biraz sonra bir daha cəhd edin. Error ${error.status}`;
    }
  };

  return (
    <>
      <Helmet>
        <title>inKredo | Giriş Et</title>
      </Helmet>
      <div className={`w-full flex flex-col ${!error && "mt-[24px]"}`}>
        <form className="w-full" onSubmit={handleSubmit(onSubmit)}>
          {error &&
            <p className="text-center text-red-500 mt-[42px] mb-[18px]">
              {getErrorMessage()}
            </p>
          }
          <div className="w-full mx-auto max-w-[300px] mb-[34px]">
            <UsernameInput disabled={isLoading} register={register} errors={errors} />
            <PasswordInput disabled={isLoading} register={register} errors={errors} />
          </div>
          <div className="flex justify-center">
            <button
              type="submit"
              className={`defButton bgGreen mx-auto block mb-[18px] ${isLoading && "disabledButton"}`}
              disabled={isLoading}
            >
              {isLoading ? "Yüklənir..." : "Daxil ol"}
            </button>
          </div>
        </form>
        <Link className={`self-center ${inputstyleclasses.noAccount}`} to={'/register'}>Hesabınız yoxdur?</Link>
      </div>
    </>
  )
}

export default Login