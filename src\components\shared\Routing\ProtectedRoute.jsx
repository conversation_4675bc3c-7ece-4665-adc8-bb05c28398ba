import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";

const ProtectedRoute = ({ children }) => {
    const access = useSelector((state) => state.auth.access);
    const navigate = useNavigate();

    useEffect(() => {
        if (!access) {
            navigate("/login");
        }
    }, [access, navigate]);

    return access ? children : null;
};

export default ProtectedRoute;
