import { useDispatch } from "react-redux"
import { handleModal, setModalData } from "@/redux/features/modal/balanceModalSlice";

function TableRow({ item }) {
    const dispatch = useDispatch();

    function handleClick() {
        dispatch(setModalData({
            title: item.title,
            name: item.propertyName,
            value: item.value
        }));
        if (item.canOpenModal) dispatch(handleModal());
    }

    return (
        <tr onDoubleClick={handleClick}>
            <td>{item.No}</td>
            <td>{item.title}</td>
            <td>{item.value}</td>
        </tr>
    )
}

export default TableRow