import inputstyleclasses from "@/pages/Auth/inputstyle.module.css"

function RepeatPassInput({ register, errors = {}, disabled = false }) {
    return (
        <p className={`${inputstyleclasses.inputStyle}`}>
            <input
                className="w-full"
                placeholder="<PERSON><PERSON><PERSON>əni təkrarla"
                type="password"
                disabled = {disabled}
                {...(register ? register("password2") : {})}
            />
            {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.password2?.message}</span>}
        </p>
    )
}

export default RepeatPassInput