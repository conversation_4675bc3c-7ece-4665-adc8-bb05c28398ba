import generalTableStyles from "@/styles/shared/generalTableStyles.module.css"
import { roundTo2 } from "@/util/formatters";
import { routeRoleAccessConfig } from "@/util/route-role-access-config/route-role-access-config";

import TableData from "./TableData";
import TableHead from "./TableHead";
import { useGetUserProfileQuery } from "@/redux/services/userApiSlice";
import { useState } from "react";
import EditInstallmentBtn from "../Buttons/EditInstallmentBtn";
import EditInstallmentModal from "../EditInstallmentModal/EditInstallmentModal";

function InstallmentTable({ contractInstallments, contractDetail }) {
    const [contractUIState, setContractUIState] = useState({
        isContextMenuOpen: false,
        top: 0,
        left: 0,
        contract: {
            id: null,
            paid_amount: null,
            isModalOpen: false
        }
    });

    console.log(contractUIState);

    const { data: user } = useGetUserProfileQuery();

    const updatedTable = [...contractInstallments.results.data
        .slice()
        .sort((a, b) => Number(a.id) - Number(b.id))
    ];

    const installmentArray = [...contractInstallments.results.data
        .slice()
        .sort((a, b) => Number(a.id) - Number(b.id))
        .filter(installment =>
            installment.payment_status !== "PAID" &&
            installment.payment_status !== "DELAYED" &&
            installment.payment_status !== "OVERPAYMENT"
        )];

    const filteredColumns = routeRoleAccessConfig.contractDetails.columns.filter(column =>
        column.roles.includes(user?.role)
    );

    function handleEditInstallmentModal() {
        setContractUIState({
            ...contractUIState,
            contract: {
                ...contractUIState.contract,
                isModalOpen: contractUIState.contract.isModalOpen ? false : true
            }
        })
    }

    const sumData = contractInstallments.results.installment_summaries;

    return (
        <>
            {contractUIState.contract.isModalOpen
                && <EditInstallmentModal
                    isOpen={contractUIState.contract.isModalOpen}
                    onClose={handleEditInstallmentModal}
                    contract={contractUIState.contract}
                />
            }
            {contractUIState.isContextMenuOpen
                && <EditInstallmentBtn
                    contractUIState={contractUIState}
                    openModal={handleEditInstallmentModal}
                />
            }
            <div className={`${generalTableStyles.tableWrapper} ${generalTableStyles.detailTable}`}>
                <table className={generalTableStyles.table}>
                    <TableHead columns={filteredColumns} />
                    <tbody className={generalTableStyles.tbody}>
                        {updatedTable.map(installment =>
                            <TableData
                                key={installment.id}
                                installment={installment}
                                status={contractDetail.status}
                                installmentArray={installmentArray}
                                columns={filteredColumns}
                                contractUIState={contractUIState}
                                setContractUIState={setContractUIState}
                            />
                        )}
                    </tbody>
                    <tfoot className={`${generalTableStyles.tfoot}`}>
                        <tr>
                            {filteredColumns.map(column => {
                                if (column.footerProp === null) {
                                    return <td key={column.accessor}></td>;
                                }
                                if (Array.isArray(column.footerProp)) {
                                    return (
                                        <td key={column.accessor}>
                                            {roundTo2(column.footerProp.reduce((sum, prop) => sum + (sumData[prop] || 0), 0))}
                                        </td>
                                    );
                                }
                                return <td key={column.accessor}>{roundTo2(Number(sumData[column.footerProp] ?? 0))}</td>;
                            })}

                        </tr>
                    </tfoot>
                </table>
            </div>
            <p className="mt-5 text-[14px]">Qeyd: kredit ödənişi etmək üçün sətrin üzərinə 2 dəfə klik edin (double click)</p>
        </>
    )
}

export default InstallmentTable