import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import DateRangePicker from "./DateRangePicker";
import { useGetAllDelaysQuery } from "@/redux/services/statsApiSlice";

const DelaysChart = () => {
  const [dateRange, setDateRange] = useState({
    fromDate: "01-05-2025",
    toDate: "09-06-2025"
  });

  const {data, isLoading, isFetching, isError, error } = useGetAllDelaysQuery(dateRange);

  const handleFromDateChange = (newDate) => {
    setDateRange(prev => ({ ...prev, fromDate: newDate }));
  };

  const handleToDateChange = (newDate) => {
    setDateRange(prev => ({ ...prev, toDate: newDate }));
  };

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Gecikmələr</h2>
        <DateRangePicker 
          fromDate={dateRange.fromDate}
          toDate={dateRange.toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
        />
      </div>
      <div style={{ height: 180 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 5, right: 20, bottom: 5, left: 0 }}>
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} domain={[0, 10000]} />
            <Tooltip />
            <Bar dataKey="value" fill="#004d99" barSize={20} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default DelaysChart;
