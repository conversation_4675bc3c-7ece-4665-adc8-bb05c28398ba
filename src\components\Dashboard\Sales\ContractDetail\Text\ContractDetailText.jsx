import classes from "./contractdetailtext.module.css"
import ProductInfo from "./ProductInfo"
import UserPartInfo from "./UserPartInfo"
import TextArea from "./TextArea"

function ContractDetailText({ contractDetail, isEdit, formRef, onSubmit }) {
    return (
        <form
            ref={formRef}
            onSubmit={onSubmit}
            className={`${classes.detailText} flex gap-[1.75rem] items-start justify-between mb-5`}
        >
            <UserPartInfo contractDetail={contractDetail} isEdit={isEdit} />
            <div className={classes.seperator}></div>
            <ProductInfo contractDetail={contractDetail} isEdit={isEdit} />
            <div className={classes.seperator}></div>
            <TextArea contractDetail={contractDetail} isEdit={isEdit} />
        </form>
    )
}

export default ContractDetailText