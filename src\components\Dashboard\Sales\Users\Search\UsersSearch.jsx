import SearchComponent from "@/components/shared/Search/SearchComponent";

function UsersSearch() {
  const searchFields = [
    {
      type: 'text',
      name: 'fullname',
      label: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> adı',
      fieldProps: { className: 'mb-4' }
    },
    {
      type: 'phone',
      name: 'phone_number',
      label: 'Telefon nömrəsi',
      fieldProps: { className: 'mb-4' }
    }
  ];

  const defaultValues = {
    fullname: '',
    phone_number: ''
  };

  return (
    <SearchComponent
      fields={searchFields}
      defaultValues={defaultValues}
      limit="16"
      formProps={{ className: "space-y-4" }}
    />
  );
}

export default UsersSearch;
