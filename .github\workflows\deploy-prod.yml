name: Deploy to VPS (Production Environment)

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Create .env.production from Secrets
        run: |
          echo "VITE_API_URL=${{ secrets.PROD_VITE_API_URL }}" > .env.production

      - name: Build Vite App (Production Mode)
        run: npm run build -- --mode=production

      - name: Deploy to Production VPS via SSH (Password Auth)
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.PROD_VPS_HOST }}
          username: ${{ secrets.PROD_VPS_USER }}
          password: ${{ secrets.PROD_VPS_PASSWORD }}
          port: ${{ secrets.PROD_VPS_PORT }}
          source: "dist/**"
          target: "/home/<USER>/public_html"
          strip_components: 1
