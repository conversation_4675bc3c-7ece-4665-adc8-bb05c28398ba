import { paymentStatusTypes } from "../constants";
import { searchType } from "../searchHelpers";

export const contractDetailsColumns = [
  {
    header: "ID",
    accessor: "id",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 128,
  },
  {
    header: "Ay №",
    accessor: "month_no",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 80,
  },
  {
    header: "Ödəyəcəyi Tarixi",
    accessor: "payment_date",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 141,
  },
  {
    header: "<PERSON><PERSON><PERSON><PERSON><PERSON> Tarix<PERSON>",
    accessor: "paid_date",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 141,
  },
  {
    header: "Ödəmə statusu",
    accessor: "payment_status",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 133,
    renderCell: (cell) => {
      return searchType(paymentStatusTypes, cell.payment_status).name;
    },
  },
  {
    header: "Aylıq investisiya",
    accessor: "monthly_investment",
    roles: ["admin"],
    footerProp: "monthly_investment_total",
    minWidth: 117,
    isNumber: true,
  },
  {
    header: "Ödənilmiş aylıq investisiya",
    accessor: "paid_monthly_investment",
    roles: ["admin"],
    footerProp: "paid_monthly_investment_total",
    minWidth: 117,
    isNumber: true,
  },
  {
    header: "Qalıq Sərmayə",
    accessor: "residual_investment",
    roles: ["admin"],
    footerProp: null,
    minWidth: 117,
    isNumber: true,
  },
  {
    header: "Qalıq Borc",
    accessor: "remaining_debt",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 117,
    isNumber: true,
  },
  {
    header: "Ümumi mənfəət",
    accessor: "total_profit",
    roles: ["admin"],
    footerProp: "total_profit_total",
    minWidth: 117,
    isNumber: true,
  },
  {
    header: "Ödənilmiş mənfəət",
    accessor: "paid_profit",
    roles: ["admin"],
    footerProp: "paid_profit_total",
    minWidth: 117,
    isNumber: true,
  },
  {
    header: "Ödəyəcəyi məb.",
    accessor: "amount",
    roles: ["admin", "manager"],
    footerProp: "total_remaining_debt",
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Ödədiyi məb.",
    accessor: "paid_amount",
    roles: ["admin", "manager"],
    footerProp: ["paid_total", "incomplete_total", "overpayment_total", "delayed_total"],
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Cərimə",
    accessor: "fine",
    roles: ["admin", "manager"],
    footerProp: "fine_total",
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Komissiya",
    accessor: "commission",
    roles: ["admin"],
    footerProp: "commission_total",
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Ödənilmiş Komissiya",
    accessor: "paid_commission",
    roles: ["admin"],
    footerProp: "paid_commission_total",
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Şirkət Balansı",
    accessor: "company_amount",
    roles: ["admin"],
    footerProp: "company_amount_total",
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Hövzə",
    accessor: "money_pool_amount",
    roles: ["admin"],
    footerProp: "money_pool_amount_total",
    minWidth: 133,
    isNumber: true,
  },
  {
    header: "Qeyd",
    accessor: "note",
    roles: ["admin", "manager"],
    footerProp: null,
    minWidth: 117,
    hasTooltip: true,
  },
];
