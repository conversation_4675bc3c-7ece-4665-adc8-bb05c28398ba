@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');

:root {
  --main-color: #073763;
  --secondary-color: #336491;
  --third-color: #92a1ca;
  --success-color: #21d25c;
  --clearall-color: #efbf3a;
  --fullpayment-color: #ccebcc;
  --incompletepayment-color: #fae9b0;
  --latepayment-color: #F1B6B6;
  --gray-color: #bdbdbd;
  --modal-input-border-color: #303030;
  --modal-placeholder-border-color: #858585;
  --cell-selected-color: #C4C6CC;
  --red-color: #CC1212;
  --table-border-color: #D1D1D14F;
}

.MuiAutocomplete-option {
  font-size: 14px !important;
}

.MuiAutocomplete-input {
  font-size: 14px !important;
}

.MuiFormLabel-root {
  font-size: 14px !important;
}

.MuiInputLabel-root {
  font-size: 14px !important;
}

.blueText {
  color: var(--main-color);
}

button {
  cursor: pointer;
}

.defButton {
  color: white;
  font-size: 14px;
  line-height: 19.07px;
  box-shadow: -1px 1px 2px 0px #00000080;
  padding: 9px 30px;
  border-radius: 32px;
  display: inline-block;

  &:disabled {
    opacity: .5;
    cursor: not-allowed;
  }
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type=number] {
  -moz-appearance: textfield;
  appearance: none;
}

.bgMain {
  background: var(--main-color);
}

.bgSecondary {
  background: var(--secondary-color);
}

.bgYellow {
  background: var(--clearall-color);
}

.bgGreen {
  background: var(--success-color);
}

.bgGray {
  background: var(--modal-placeholder-border-color);
}

.bgRed {
  background-color: red;
  color: white;
}

#root>.MuiBox-root {
  display: block !important;
}

.MuiPagination-ul {
  margin: 0;
  gap: 11px;
}

.MuiFormControl-root {
  margin: 0 !important;
}

.react-datepicker-wrapper {
  width: 100%;
  border-bottom: 1px solid gray !important;
  outline: 0 !important;
}

.disabledButton {
  opacity: .5;
  cursor: not-allowed;
}

.errorText {
  color: red;
  margin-top: 40px;
  margin-left: 10px;
}

.paginationWrapper {
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 4px;
  width: 85%;
}

.MuiPagination-ul {
  margin: 0;
  gap: 11px;
  flex-wrap: nowrap !important;
  width: fit-content;
}

.formElement {
  flex-direction: column;
  padding-bottom: 90px;
}

.modalTitle {
  font: 600 18px Arial;
  line-height: 21.78px;
  text-align: center;
  text-underline-position: from-font;
  color: var(--main-color);
}

.nav-link-transition {
  transition: background-color 0.3s ease, color 0.3s ease;
}

@media screen and (min-width: 600px) {
  .MuiBox-root {
    padding-right: 0 !important;
    padding-bottom: 0 !important;
  }

  #root>.MuiBox-root {
    display: flex !important;
  }
}

@media screen and (min-width: 992px) {
  .formElement {
    flex-direction: row;
  }
}
