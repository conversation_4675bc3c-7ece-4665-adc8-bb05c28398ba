export function objectFormatter(obj) {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => [
      key,
      value === "" ? null : value,
    ])
  );
}

export function searchDataRefractorer(obj) {
  return Object.fromEntries(
    Object.entries(obj).filter(([key, value]) => value)
  );
}

export function roundTo2(num) {
  return Math.round(num * 100) / 100;
}

export function formatBalanceData(balance) {
  return {
    assetsData: [
      {
        No: "1",
        title: "Kassa",
        value: balance.assets.cash,
        propertyName: "money_pool_available_amount",
        canOpenModal: true,
      },
      {
        No: "3",
        title: "Alacaqlar",
        value: balance.assets.receivables,
        canOpenModal: false,
      },
      {
        No: "4",
        title: "Ticarət Malları",
        value: balance.assets.trade_goods,
        canOpenModal: false,
      },
      {
        No: "8",
        title: "<PERSON><PERSON><PERSON>ə<PERSON><PERSON><PERSON> dövrün xə<PERSON>",
        value: balance.assets.future_period_expenses,
        canOpenModal: false,
      },
    ],
    liabilityData: [
      {
        No: "5",
        title: "Adil Finans",
        value: balance.liabilities.company_balance,
        propertyName: "company_balance",
        canOpenModal: true,
      },
      {
        No: "5.1.1",
        title: "Şirkətin bölünməmiş mənfəəti",
        value: balance.liabilities.retained_earnings_of_the_company,
        propertyName: "retained_earnings_of_the_company",
        canOpenModal: true,
      },
      {
        No: "6",
        title: "Hövzə",
        value: balance.liabilities.money_pool,
        propertyName: "money_pool_capacity_amount",
        canOpenModal: true,
      },
      {
        No: "6.1",
        title: "Hövzə bölünməmiş mənfəət",
        value: balance.liabilities.unallocated_profit_of_the_money_pool,
        propertyName: "unallocated_profit_of_the_money_pool",
        canOpenModal: true,
      },
      {
        No: "7",
        title: "Komissiya",
        value: balance.liabilities.commission,
        canOpenModal: false,
      },
      {
        No: "7.1",
        title: "Xərc",
        propertyName: "commission",
        value: balance.liabilities.cost,
        canOpenModal: true,
      },
      {
        No: "9",
        title: "Gələcək dövrün mənfəəti",
        value: balance.liabilities.future_period_profit,
        canOpenModal: false,
      },
    ],
  };
}

export const transformText = (data, text) => {
  if (
    data.operation_type === "INVESTMENT" ||
    data.operation_type === "WITHDRAW" ||
    data.operation_type === "TRANSFER" ||
    data.operation_type === "SALE" ||
    data.operation_type === "USER_UPDATE" ||
    data.operation_type === "CREATE_WALLET" ||
    data.operation_type === "USER_CREATE"
  ) {
    let startIndex = text.indexOf(data?.user?.fullname);
    let linkText = data?.user?.fullname;
    let linkTo = `/sales/users/${data?.user?.id}`;
    let type = "investor";

    if (startIndex === -1 && data?.user?.fullname) {
      const nameParts = data.user.fullname.split(" ");
      if (nameParts.length > 0) {
        const firstName = nameParts[0];
        startIndex = text.indexOf(firstName);
        if (startIndex !== -1) {
          linkText = firstName;
        }
      }
    }

    if (
      startIndex === -1 &&
      data.operation_type === "SALE" &&
      data.contract?.customer?.fullname
    ) {
      const customerName = data.contract.customer.fullname;
      startIndex = text.indexOf(customerName);
      linkText = customerName;
      linkTo = `/sales/users/${data.contract.customer.id}`;
      type = "investor";

      if (startIndex === -1) {
        const nameParts = customerName.split(" ");
        if (nameParts.length > 0) {
          const firstName = nameParts[0];
          startIndex = text.indexOf(firstName);
          if (startIndex !== -1) {
            linkText = firstName;
          }
        }
      }
    }

    if (startIndex !== -1) {
      return {
        type,
        before: text.substring(0, startIndex),
        linkText,
        linkTo,
        after: text.substring(startIndex + linkText.length),
      };
    }
  } else if (
    data.operation_type === "CREDIT_PAYMENT" ||
    data.operation_type === "UPDATE_INSTALLMENT" ||
    data.operation_type === "CONTRACT_UPDATE"
  ) {
    const afIdRegex = /AF\d+/;
    const match = text.match(afIdRegex);

    if (match && match[0] && data.contract?.id) {
      const contractId = match[0];
      const parts = text.split(contractId);

      return {
        type: "contract",
        before: parts[0],
        linkText: contractId,
        linkTo: `/sales/contracts/${data.contract.id}`,
        after: parts[1] || "",
      };
    }
  }

  return { type: "plain", text };
};
