import inputstyleclasses from "@/pages/Auth/inputstyle.module.css"

function FullnameInput({register, errors = {}, disabled = false}) {
    return (
        <p className={`${inputstyleclasses.inputStyle}`}>
            <input
                className="w-full"
                placeholder="Ad, soyad, ata adı"
                type="text"
                disabled={disabled}
                {...(register ? register("fullname") : {})}
            />
            {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.fullname?.message}</span>}
        </p>
    )
}

export default FullnameInput