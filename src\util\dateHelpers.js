export function getToday() {
  const azerbaijanDate = new Date().toLocaleString("en-GB", {
    timeZone: "Asia/Baku",
  });
  const [day, month, year] = azerbaijanDate.split(",")[0].split("/");
  return `${day}-${month}-${year}`;
}

export function extractDMY(date) {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    return null;
  }

  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}

export function formatToDMY(dateString) {
  return dateString.split("-").reverse().join("-");
}

export function convertToDate(dateString) {
  if (typeof dateString !== "string") return null;

  const parts = dateString.split("-");
  if (parts.length !== 3) return null;

  const [day, month, year] = parts.map(Number);

  if (isNaN(day) || isNaN(month) || isNaN(year)) return null;

  const date = new Date(year, month - 1, day);
  if (
    date.getFullYear() !== year ||
    date.getMonth() !== month - 1 ||
    date.getDate() !== day
  ) {
    return null;
  }

  return date;
}

// Utility function to convert YYYY-MM-DD to DD-MM-YYYY format for API
export function formatDateForAPI(dateString) {
  const [year, month, day] = dateString.split('-');
  return `${day}-${month}-${year}`;
}