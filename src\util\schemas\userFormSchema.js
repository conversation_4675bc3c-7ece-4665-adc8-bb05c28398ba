import * as yup from "yup";
import { emailRegex } from "./profileSchema";

export const userFormSchema = yup.object().shape({
  fullname: yup.string().required("Ad Soyad Ata adı daxil edin"),

  phone1: yup.string().required("Nömrə daxil edin"),

  phone2: yup.string(),

  city: yup.string().required("Şəhər daxil edin"),

  investor_balance: yup
    .number()
    .when("$isInvestorBalance", ([isInvestorBalance], schema) =>
      isInvestorBalance
        ? schema.required("Balans daxil edin").typeError("Balans daxil edin")
        : schema.notRequired()
    ),

  email: yup
    .string()
    .test("valid-email", "E-mail düzgün formatda deyil", (value) => {
      if (!value) return true;
      return emailRegex.test(value);
    }),

  role: yup.string().when("$isAdmin", {
    is: true,
    then: (schema) => schema.required("Rol seçilməlidir"),
    otherwise: (schema) => schema.notRequired(),
  }),

  // portmanat1: yup
  //   .number()
  //   .required("Portmanat daxil edin")
  //   .typeError("Portmanat daxil edin"),
});
