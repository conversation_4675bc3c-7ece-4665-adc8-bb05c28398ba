import { Box, Drawer } from "@mui/material"

import { useDispatch, useSelector } from "react-redux";
import { handleDrawerClose, handleDrawerTransitionEnd } from "@/redux/features/layout/layoutSlice";
import DrawerComponent from "./Drawer"

function AsideContent() {
    const {
        drawerWidth,
        mobileOpen,
    } = useSelector(state => state.layout);

    const dispatch = useDispatch();

    return (
        <Box
            component="nav"
            sx={{
                width: { sm: drawerWidth },
                flexShrink: { sm: 0 },
                transition: 'width 0.3s ease',
                overflow: "hidden"
            }}
            aria-label="mailbox folders"
        >
            <Drawer
                variant="temporary"
                open={mobileOpen}
                onTransitionEnd={() => dispatch(handleDrawerTransitionEnd())}
                onClose={() => dispatch(handleDrawerClose())}
                ModalProps={{
                    keepMounted: true,
                }}
                sx={{
                    display: { xs: 'block', sm: 'none' },
                    '& .MuiDrawer-paper': {
                        boxSizing: 'border-box',
                        width: drawerWidth,
                        transition: 'width 0.3s ease',
                        overflow: "hidden"
                    },
                }}
            >
                <DrawerComponent />
            </Drawer>
            <Drawer
                variant="permanent"
                sx={{
                    display: { xs: 'none', sm: 'block' },
                    '& .MuiDrawer-paper': {
                        boxSizing: 'border-box',
                        width: drawerWidth,
                        transition: 'width 0.3s ease',
                        overflow: "hidden"
                    },
                }}
                open
            >
                <DrawerComponent />
            </Drawer>
        </Box>
    )
}

export default AsideContent
