import Box from '@mui/material/Box';
import CssBaseline from '@mui/material/CssBaseline';
import Toolbar from '@mui/material/Toolbar';
import { Outlet } from 'react-router-dom';
import { memo } from 'react';

import AsideContent from './Aside/AsideContent.jsx';
import LayoutHeader from './LayoutHeader/LayoutHeader.jsx';
import SearchLayout from './SearchLayout/SearchLayout.jsx';

import MainLoadingScreen from './LoadingScreen/MainLoadingScreen.jsx';
import { ToastContainer } from 'react-toastify';
import "react-toastify/dist/ReactToastify.css";
import { useSelector } from 'react-redux';
import { useGetUserProfileQuery } from '@/redux/services/userApiSlice.js';
import ScrollToTop from '@/components/shared/Routing/ScrollToTop.jsx';

const MemoizedAsideContent = memo(AsideContent);
const MemoizedMainContent = memo(MainContent);
const MemoizedLayoutHeader = memo(LayoutHeader);

function Layout() {
  const { isLoading: profileLoading } = useGetUserProfileQuery();
  const logoutLoading = useSelector((state) => state.auth.logoutLoading);

  if (profileLoading || logoutLoading) return <MainLoadingScreen />;

  return (
    <Box sx={{ display: 'flex' }}>
      <ToastContainer />
      <CssBaseline />
      <MemoizedLayoutHeader />
      <MemoizedAsideContent />
      <MemoizedMainContent />
    </Box>
  );
}

function MainContent() {
  const { drawerWidth } = useSelector(state => state.layout);

  return (
    <Box
      component="main"
      sx={{ flexGrow: 1, p: 3, width: { sm: `calc(100% - ${drawerWidth}px)` }, transition: 'width 0.3s ease' }}
    >
      <ScrollToTop />
      <Toolbar />
      <div className="mt-3">
        <div className={`flex justify-between items-stretch`}>
          <Outlet />
        </div>
      </div>
      <SearchLayout />
    </Box>
  );
};

export default Layout;