import { createApi } from "@reduxjs/toolkit/query/react";
import { authBaseQueryWithReauth } from "./baseQueryWithReauth";

export const userApiSlice = createApi({
  baseQuery: authBaseQueryWithReauth,
  reducerPath: "userApiSlice",
  tagTypes: ["Users", "SingleUser", "Profile"],
  endpoints: (builder) => ({
    getAllCities: builder.query({
      query: () => ({
        url: "/users/cities/",
        method: "GET",
        params: { limit: 1000 },
      }),
    }),
    getUserProfile: builder.query({
      query: () => "/users/me/",
      providesTags: ["Profile"],
    }),
    updateUserProfile: builder.mutation({
      query: (credentials) => ({
        url: "/users/me/",
        method: "PUT",
        body: credentials,
      }),
      invalidatesTags: ["Profile"],
    }),
    getAllUsers: builder.query({
      query: (params) => ({
        url: "/users/",
        method: "GET",
        params,
      }),
      providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getSingleUsers: builder.query({
      query: (id) => ({
        url: `/users/${id}/`,
        method: "GET",
      }),
      providesTags: ["SingleUser"],
      keepUnusedDataFor: 1,
    }),
    addUser: builder.mutation({
      query: (credentials) => ({
        url: "/users/create/",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Users", "SingleUser"],
    }),
    updateUser: builder.mutation({
      query: ({ data, id }) => ({
        url: `/users/${id}/`,
        method: "PUT",
        body: data,
      }),
      invalidatesTags: ["Users", "SingleUser"],
    }),
    logoutUser: builder.mutation({
      query: (credentials) => ({
        url: `/users/logout/`,
        method: "POST",
        body: credentials,
      }),
    }),
  }),
});

export const {
  useGetAllCitiesQuery,
  useGetUserProfileQuery,
  useUpdateUserProfileMutation,
  useGetAllUsersQuery,
  useGetSingleUsersQuery,
  useAddUserMutation,
  useUpdateUserMutation,
  useLogoutUserMutation,
} = userApiSlice;
