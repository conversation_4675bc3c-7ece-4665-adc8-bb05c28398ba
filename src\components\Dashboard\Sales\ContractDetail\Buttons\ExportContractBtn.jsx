import { useExportContractMutation } from "@/redux/services/contractApiSlice";
import { errorFinder } from "@/util/errorHandler";
import { showToast } from "@/util/showToast";
import { useParams } from "react-router-dom";

function ExportContractBtn() {
    const { id } = useParams();

    const [exportContract, { isLoading: exportContractLoading }] = useExportContractMutation();

    async function handleContractExport() {
        try {
            const data = {
                contract_id: id,
            };

            const result = await exportContract(data);

            if (result.data && result.data.success) {
                showToast("Export uğurla tamamlandı.", "success");
            } else {
                throw new Error("Müqaviləni yükləmək mümkün olmadı.");
            }
        }
        catch (error) {
            console.log(error);
            const msg = errorFinder(error);
            showToast(msg, "error");
        }
    }

    return (
        <button
            disabled={exportContractLoading}
            className="defButton bgGreen mb-5"
            onClick={handleContractExport}
        >
            {exportContractLoading ? "Export Edilir..." : "Export Et"}
        </button>
    )
}

export default ExportContractBtn;
