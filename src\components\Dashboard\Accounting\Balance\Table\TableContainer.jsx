
import generalTableStyles from "@/styles/shared/generalTableStyles.module.css"
import { TableHeader } from "./TableHeader"

function TableContainer({ children, label, sum }) {
    return (
        <div>
            <h2 className={generalTableStyles.title}>{label}</h2>
            <table className={generalTableStyles.table}>
                <TableHeader />
                {children}
                <tfoot className={generalTableStyles.tfoot}>
                    <tr>
                        <td colSpan={2}></td>
                        <td>{sum}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    )
}

export default TableContainer