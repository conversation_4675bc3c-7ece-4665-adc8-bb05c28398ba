import { createApi } from "@reduxjs/toolkit/query/react";
import { authBaseQueryWithReauth } from "./baseQueryWithReauth";

export const statsApiSlice = createApi({
  baseQuery: authBaseQueryWithReauth,
  reducerPath: "statsApiSlice",
//   tagTypes: ["Users", "SingleUser", "Profile"],
  endpoints: (builder) => ({
    getAllCosts: builder.query({
      query: (params) => ({
        url: "/stats/costs/",
        method: "GET",
        params,
      }),
    //   providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getAllDelays: builder.query({
      query: (params) => ({
        url: "/stats/delay/",
        method: "GET",
        params,
      }),
    //   providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getAllIncome: builder.query({
      query: (params) => ({
        url: "/stats/income/",
        method: "GET",
        params,
      }),
    //   providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getAllInstallments: builder.query({
      query: (params) => ({
        url: "/stats/installments/",
        method: "GET",
        params,
      }),
    //   providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getAllInvestments: builder.query({
      query: (params) => ({
        url: "/stats/investments/",
        method: "GET",
        params,
      }),
    //   providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
    getAllSales: builder.query({
      query: (params) => ({
        url: "/stats/sales/",
        method: "GET",
        params,
      }),
    //   providesTags: ["Users"],
      keepUnusedDataFor: 0,
    }),
  }),
});

export const {
  useGetAllCostsQuery,
  useGetAllDelaysQuery,
  useGetAllIncomeQuery,
  useGetAllInstallmentsQuery,
  useGetAllInvestmentsQuery,
  useGetAllSalesQuery,
} = statsApiSlice;
