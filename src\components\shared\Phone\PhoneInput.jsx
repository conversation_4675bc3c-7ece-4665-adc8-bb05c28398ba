import { MuiTelInput, matchIsValidTel } from "mui-tel-input";
import { Controller } from "react-hook-form";
import { useRef } from "react";

function PhoneInput({
    control, name, defaultValue = "", rules = {}, errors = {}, onChange, ...props
}) {
    const isRHF = !!control;
    const inputRef = useRef(null);

    if (isRHF) {
        return (
            <Controller
                name={name}
                control={control}
                defaultValue={defaultValue}
                rules={{
                    ...rules,
                    validate: v =>
                        v === "" || matchIsValidTel(v) || "Telefon nömrəsi düzgün deyil",
                }}
                render={({ field }) => {
                    const handleChange = (val) => {
                        // 1) record caret
                        const node = inputRef.current;
                        const caret = node?.selectionStart;

                        // 2) update form state
                        field.onChange(val);
                        onChange?.(val);

                        // 3) restore caret next tick
                        window.requestAnimationFrame(() => {
                            if (node && caret != null) {
                                node.setSelectionRange(caret, caret);
                            }
                        });
                    };

                    return (
                        <MuiTelInput
                            {...field}
                            label={props.label}
                            defaultCountry="AZ"
                            variant="standard"
                            forceCallingCode
                            onChange={handleChange}
                            error={!!errors[name]}
                            helperText={errors[name]?.message}
                            // attach ref to the <input> itself
                            inputProps={{
                                ...props.inputProps,
                                ref: inputRef
                            }}
                            {...props}
                        />
                    );
                }}
            />
        );
    }

    return (
        <MuiTelInput
            name={name}
            label={props.label}
            value={props.value}
            onChange={onChange}
            defaultCountry="AZ"
            variant="standard"
            forceCallingCode
            inputProps={{
                ...props.inputProps,
                // you can also attach ref here if you need cursor restore
            }}
            {...props}
        />
    );
}

export default PhoneInput;
