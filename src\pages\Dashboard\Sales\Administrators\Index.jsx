import { Link, useSearchParams } from "react-router-dom";
import classes from "@/styles/shared/usersgrid.module.css";
import { Helmet } from "react-helmet-async"
import { useSelector } from "react-redux";
import { useGetAllUsersQuery } from "@/redux/services/userApiSlice";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import AdministratorGridItem from "@/components/Dashboard/Sales/Administrators/AdministratorGridItem";

function AdministratorsPage() {
  const [searchParams] = useSearchParams();

  const limit = parseInt(searchParams.get("limit") || "16", 10);
  const offset = parseInt(searchParams.get("offset") || "0", 10);

  const { isSearchOpen } = useSelector(state => state.layout);

  const queryObject = {
    ...Object.fromEntries(searchParams.entries()),
    limit,
    offset,
    is_employee: true
  };

  const { data: administrators, isLoading, isError, error } = useGetAllUsersQuery(queryObject);

  if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

  return (
    <>
      <Helmet>
        <title>
          inKredo | İdarəçilər
        </title>
      </Helmet>
      <div className={`${classes.usersWrapper} ${isSearchOpen && classes.searchOpened}`}>
        <Link to="new" className="defButton bgSecondary">
          İdarəçi əlavə et
        </Link>
        {isLoading ? (
          <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
            <ContentLoadingScreen />
          </div>
        ) : (
          administrators && administrators.results.length > 0
            ? <div className={`${classes.usersGrid} flex flex-wrap gap-[30px] mt-[24px]`}>
              {administrators.results.map((user, index) => (
                <AdministratorGridItem user={user} key={user.id || index} />
              ))}
            </div>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
        {administrators
          && !isLoading
          && (
            <PaginationComponent
              totalCount={administrators.count}
            />
          )}
      </div>
    </>
  )
}

export default AdministratorsPage