import { Autocomplete, CircularProgress, TextField } from "@mui/material";
import { usePaginatedUsers } from "@/hooks/usePaginatedUsers";
import { useDispatch, useSelector } from "react-redux";
import { setData } from "@/redux/features/infiniteScrollSlice";
import { useParams } from "react-router-dom";
import { useEffect, useRef, useCallback } from "react";
import { useGetSingleUsersQuery } from "@/redux/services/userApiSlice";

function AutocompleteWrapper({
    type,
    label,
    children,
    queryParams = {},
    defaultValue = {},
    isOptionalField = false
}) {
    const { customerParamId } = useParams();
    const dispatch = useDispatch();
    const observerRef = useRef(null);
    const hasInteracted = useRef(false);
    const didInit = useRef(false);

    const { fullname, isListOpen, selectedValue, error } =
        useSelector(state => state.infiniteScroll[type]);
    const autocompleteData = useSelector(state => state.infiniteScroll);

    useEffect(() => {
        if (
            !didInit.current &&
            defaultValue?.id != null &&
            type !== "customer" &&
            !selectedValue
        ) {
            dispatch(setData({
                mainKey: type,
                key: "selectedValue",
                value: defaultValue
            }));
            didInit.current = true;
        }
    }, [defaultValue, selectedValue, type, dispatch]);

    const { data: user } = useGetSingleUsersQuery(customerParamId, {
        skip: !customerParamId || type !== "customer"
    });

    useEffect(() => {
        if (type === "customer" && user && customerParamId) {
            dispatch(setData({
                mainKey: "customer",
                key: "selectedValue",
                value: { fullname: user.fullname, customerParamId }
            }));
        }
    }, [user, customerParamId, type, dispatch]);

    const { users, isLoading, isFetching, hasMore, loadMore, reset } =
        usePaginatedUsers(isListOpen, fullname, queryParams);

    const lastOptionRef = useCallback(
        node => {
            if (isLoading || isFetching) return;
            if (observerRef.current) observerRef.current.disconnect();
            observerRef.current = new IntersectionObserver(entries => {
                if (entries[0].isIntersecting && hasMore) {
                    loadMore();
                }
            });
            if (node) observerRef.current.observe(node);
        },
        [isLoading, isFetching, hasMore, loadMore]
    );

    const customerId = Number(autocompleteData.customer?.selectedValue?.id);
    const attorneyId = autocompleteData.attorney?.selectedValue?.id;
    const guarantorId = autocompleteData.guarantor?.selectedValue?.id;
    let updatedUsers = users;
    if ((type === "guarantor" || type === "attorney") && customerId) {
        updatedUsers = users.filter(u => u.id !== customerId);
    } else if (type === "customer" && (attorneyId || guarantorId)) {
        updatedUsers = users.filter(u => {
            if (attorneyId && u.id === attorneyId) return false;
            if (guarantorId && u.id === guarantorId) return false;
            return true;
        });
    }

    return (
        <>
            <div
                className={`flex ${error && !isOptionalField ? "items-center" : "items-end"
                    } justify-between my-[14px]`}
            >
                <span className="w-1/3 text-[14px]">{label}</span>
                <Autocomplete
                    id={`userSelector-${type}`}
                    size="small"
                    className="w-2/3"
                    onOpen={() =>
                        dispatch(
                            setData({ mainKey: type, key: "isListOpen", value: true })
                        )
                    }
                    onClose={() => {
                        dispatch(
                            setData({ mainKey: type, key: "isListOpen", value: false })
                        );
                        reset();
                    }}
                    options={updatedUsers}
                    getOptionLabel={option => option.fullname || ""}
                    loading={isLoading}
                    isOptionEqualToValue={(option, value) => option.id === value?.id}
                    onChange={(_, newVal) => {
                        hasInteracted.current = true;
                        dispatch(
                            setData({ mainKey: type, key: "selectedValue", value: newVal })
                        );
                        dispatch(setData({ mainKey: type, key: "error", value: false }));
                    }}
                    value={selectedValue}
                    onInputChange={(_, newInputValue) =>
                        dispatch(
                            setData({ mainKey: type, key: "fullname", value: newInputValue })
                        )
                    }
                    renderOption={(props, option, state) => {
                        const isLast = state.index === updatedUsers.length - 1;
                        return (
                            <li
                                {...props}
                                key={option.id}
                                ref={isLast ? lastOptionRef : undefined}
                            >
                                {option.fullname}
                                {isLast && (isLoading || isFetching) && <em> Yüklənilir…</em>}
                            </li>
                        );
                    }}
                    renderInput={params => (
                        <TextField
                            {...params}
                            variant="standard"
                            error={!isOptionalField && error}
                            helperText={
                                !isOptionalField && error ? "Bu hissə boş ola bilməz" : ""
                            }
                            InputProps={{
                                ...params.InputProps,
                                endAdornment: (
                                    <>
                                        {(isLoading || isFetching) && (
                                            <CircularProgress size={20} />
                                        )}
                                        {params.InputProps.endAdornment}
                                    </>
                                )
                            }}
                        />
                    )}
                />
            </div>
            {children}
        </>
    );
}

export default AutocompleteWrapper;