import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";

function InvestorItem({ investor, modalHandler }) {
    return (
        <tr>
            <td className={generalTableStyles.tdStyle} title={investor.id}>{investor.id}</td>
            <td className={generalTableStyles.tdStyle} title={investor?.created_date}>{investor?.created_date}</td>
            <td onClick={() => modalHandler("portmanatPreview", investor.user?.id)} className={`${generalTableStyles.tdStyle} cursor-pointer`} title={investor.user?.fullname}>{investor.user?.fullname}</td>
            <td className={generalTableStyles.tdStyle} title={investor.period_in_months}>{investor.period_in_months}</td>
            <td className={generalTableStyles.tdStyle} title={investor.previous_balance}>{investor.previous_balance}</td>
            <td className={generalTableStyles.tdStyle} title={investor.money_pool_share_coefficient}>{investor.money_pool_share_coefficient}</td>
            <td className={generalTableStyles.tdStyle} title={investor.monthly_profit}>{investor.monthly_profit}</td>
            <td className={generalTableStyles.tdStyle} title={investor.balance}>{investor.balance}</td>
        </tr>
    )
}

export default InvestorItem