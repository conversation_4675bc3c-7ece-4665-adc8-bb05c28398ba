import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { setData } from "@/redux/features/infiniteScrollSlice";
import { Helmet } from "react-helmet-async";
import { useAddContractMutation } from "@/redux/services/contractApiSlice";
import { objectFormatter } from "@/util/formatters";
import { errorFinder } from "@/util/errorHandler";

import CustomerInfo from "@/components/Dashboard/Sales/AddContract/CustomerInfo";
import ContractInfo from "@/components/Dashboard/Sales/AddContract/ContractInfo";
import { showToast } from "@/util/showToast";

const autocompleteKeys = ["customer", "guarantor", "attorney"];

function AddContractPage() {
  const [status, setStatus] = useState("ONGOING");

  const [addContract, { isLoading }] = useAddContractMutation();
  const autoCompleteData = useSelector(state => state.infiniteScroll);

  const navigate = useNavigate();
  const dispatch = useDispatch();

  async function handleSubmit(e) {
    e.preventDefault();

    let isError = false;

    autocompleteKeys.forEach(key => {
      let isIncluded = !!autoCompleteData[key]?.selectedValue?.id;

      console.log(isIncluded);

      dispatch(setData({
        mainKey: key,
        key: "error",
        value: !isIncluded
      }));

      (!isIncluded && key != "guarantor" && key != "attorney") && (isError = true);
    });

    console.log(isError);

    if (isError) return;

    const fd = new FormData(e.target);
    const data = Object.fromEntries(fd.entries());

    if (data.monthly_payment < 0) data.monthly_payment = 1;

    console.log(status);

    try {
      data.customer = autoCompleteData.customer.selectedValue.id;
      data.guarantor = autoCompleteData.guarantor.selectedValue?.id || null;
      data.attorney = autoCompleteData.attorney?.selectedValue?.id || null;
      data.status = status;

      data["contract_date"] = data["contract_date"].split("-").reverse().join("-");

      await addContract(objectFormatter(data)).unwrap();

      navigate("../contracts?limit=16&offset=0", {
        relative: true
      })
    } catch (err) {
      console.log(err);

      const msg = errorFinder(err);
      showToast(msg, "error");
    }
  }

  return (
    <>
      <Helmet>
        <title>inKredo | Müqavilə yarat</title>
      </Helmet>
      <div className="mt-6 h-full pr-9">
        <form onSubmit={handleSubmit} className="flex flex-wrap gap-[7vw] formElement">
          <CustomerInfo />
          <ContractInfo />
          <div className="fixed bottom-8 right-10 flex items-center gap-[12px]">
            <button
              disabled={isLoading}
              onClick={() => setStatus("WAITING")}
              className="defButton bgGray">Gözləmə</button>
            <button
              disabled={isLoading}
              onClick={() => setStatus("ONGOING")}
              className="defButton bgGreen">Təstiq et</button>
          </div>
        </form>
      </div>
    </>
  );
}

export default AddContractPage;
