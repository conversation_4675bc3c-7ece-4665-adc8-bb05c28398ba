import swal from "sweetalert";

import { fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import { clearAuth, setCredentials } from "@/redux/features/authSlice";

const API_URL = import.meta.env.VITE_API_URL;

export const publicBaseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  credentials: "include",
});

const authBaseQuery = fetchBaseQuery({
  baseUrl: API_URL,
  credentials: "include",
  prepareHeaders: (headers, { getState }) => {
    const token = getState().auth.access;

    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

let isRefreshing = false;
let refreshQueue = [];

export const authBaseQueryWithReauth = async (args, api, extraOptions) => {
  let result = await authBaseQuery(args, api, extraOptions);

  if (result.error && result.error.status === 401) {
    if (isRefreshing) {
      return new Promise((resolve, reject) => {
        refreshQueue.push({ resolve, reject });
      }).then((newToken) => {

        let newArgs = typeof args === "string" ? { url: args } : { ...args };
        newArgs.headers = {
          ...newArgs.headers,
          Authorization: `Bearer ${newToken}`,
        };
        return authBaseQuery(newArgs, api, extraOptions);
      });
    }

    isRefreshing = true;

    try {
      const { refresh } = api.getState().auth;
      if (!refresh) {
        throw new Error("No refresh token available");
      }

      const refreshResult = await publicBaseQuery(
        {
          url: "/users/token/refresh/",
          method: "POST",
          body: { refresh },
        },
        api,
        extraOptions
      );

      if (refreshResult.error) {
        throw new Error("Refresh request failed with error");
      }
      if (!refreshResult.data?.access) {
        throw new Error("No valid 'access' field returned");
      }

      const newaccess = refreshResult.data.access;

      api.dispatch(setCredentials({ access: newaccess, refresh }));

      refreshQueue.forEach(({ resolve }) => resolve(newaccess));
      refreshQueue = [];

      let newArgs = typeof args === "string" ? { url: args } : { ...args };
      newArgs.headers = {
        ...newArgs.headers,
        Authorization: `Bearer ${newaccess}`,
      };
      result = await authBaseQuery(newArgs, api, extraOptions);
    } catch (error) {
      refreshQueue.forEach(({ reject }) => reject(error));
      refreshQueue = [];

      swal({
        title: "Sessiya müddəti bitti, xahiş edirik yenidən giriş edin",
        icon: "error",
      }).then(() => {
        api.dispatch(clearAuth());
      });

      return {
        error: {
          status: 401,
          data: {
            message: "Sessiya müddəti bitti, xahiş edirik yenidən giriş edin",
            code: "AUTH_SESSION_EXPIRED"
          }
        }
      };
    } finally {
      isRefreshing = false;
    }
  }

  return result;
};
