import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { searchDataRefractorer } from "@/util/formatters";
import { convertToDate, extractDMY } from "@/util/dateHelpers";
import { searchType } from "@/util/searchHelpers";
import <PERSON><PERSON>ield from "./SearchField";
import { clearSelectedCity } from "@/redux/features/citySlice";
import { useDispatch } from "react-redux";

function SearchComponent({
  fields = [],
  defaultValues = {},
  limit = "16",
  formProps = {},
  showResetButton = true
}) {
  const [searchParams, setSearchParams] = useSearchParams();
  const dispatch = useDispatch();

  const [formState, setFormState] = useState(() => {
    const initial = {};

    fields.forEach(field => {
      const { name, type } = field;
      const param = searchParams.get(name);

      if (type === "date") {
        initial[name] = [
          convertToDate(searchParams.get(`start_${name}`)),
          convertToDate(searchParams.get(`end_${name}`))
        ];
      } else if (type === "phone") {
        initial[name] = param?.replace("+994", "") || "";
      } else if (type === "select" && field.options) {
        initial[name] = param || "";
        initial[`${name}_display`] = searchType(field.options, param);
      } else if (type === "city") {
        initial[name] = searchParams.get("customer__city") || "";
      } else {
        initial[name] = param || "";
      }
    });

    return { ...defaultValues, ...initial };
  });

  useEffect(() => {
    if (!Object.keys(formState).length) return;

    const handler = setTimeout(() => {
      const current = Object.fromEntries(searchParams.entries());

      fields.forEach(field => {
        const { name, type } = field;

        if (type === "date") {
          const [start, end] = formState[name] || [null, null];
          current[`start_${name}`] = extractDMY(start);
          current[`end_${name}`] = extractDMY(end);
        } else if (type === "phone") {
          current[name] = formState[name] ? `+994${formState[name]}` : "";
        } else if (type === "city") {
          current["customer__city"] = formState[name] || "";
        } else {
          current[name] = formState[name] || "";
        }
      });

      setSearchParams(searchDataRefractorer(current), { replace: true });
    }, 500);

    return () => clearTimeout(handler);
  }, [formState, searchParams, setSearchParams, fields]);

  const resetOffset = () => {
    const current = Object.fromEntries(searchParams.entries());
    current.offset = "0";
    setSearchParams(searchDataRefractorer(current), { replace: true });
  };

  const handleChange = e => {
    const { name, value } = e.target;
    setFormState(s => ({ ...s, [name]: value }));
    resetOffset();
  };

  const resetForm = e => {
    e.preventDefault();
    setFormState(defaultValues);
    setSearchParams({ limit, offset: "0" }, { replace: true });
    dispatch(clearSelectedCity());
  };

  return (
    <form onSubmit={e => e.preventDefault()} {...formProps}>
      {fields.map((field, i) => (
        <SearchField
          key={i}
          {...field}
          value={
            field.type === "select" && field.useDisplayValue
              ? formState[`${field.name}_display`]
              : formState[field.name]
          }
          onChange={handleChange}
        />
      ))}

      {showResetButton && (
        <button
          type="reset"
          onClick={resetForm}
          className="block defButton bgYellow ml-auto"
        >
          Təmizlə
        </button>
      )}
    </form>
  );
}

export default SearchComponent;