import { createApi } from "@reduxjs/toolkit/query/react";
import { publicBaseQuery } from "./baseQueryWithReauth";

export const publicApiSlice = createApi({
    baseQuery: publicBaseQuery,
    reducerPath : "publicApiSlice",
    endpoints: (builder) => ({
        login: builder.mutation({
            query: (credentials) => ({
                url: "/users/token/",
                method: "POST",
                body: credentials,
            }),
            invalidatesTags : [],
        }),
        register: builder.mutation({
            query: (userData) => ({
                url: "/users/register/",
                method: "POST",
                body: userData,
            }),
            invalidatesTags : []
        }),
    }),
});

export const { useLoginMutation, useRegisterMutation } = publicApiSlice;