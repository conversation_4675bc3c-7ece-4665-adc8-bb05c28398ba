<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   id="Layer_1"
   data-name="Layer 1"
   viewBox="0 0 1000 1000"
   version="1.1"
   sodipodi:docname="inkredo launcher icon.trasnparent.svg"
   inkscape:version="1.4 (86a8ad7, 2024-10-11)"
   width="1000"
   height="1000"
   xml:space="preserve"
   inkscape:export-filename="inkredo launcher icon.png"
   inkscape:export-xdpi="401.24081"
   inkscape:export-ydpi="401.24081"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"><sodipodi:namedview
     id="namedview4"
     pagecolor="#dcdcdc"
     bordercolor="#eeeeee"
     borderopacity="1"
     inkscape:showpageshadow="0"
     inkscape:pageopacity="0"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#505050"
     inkscape:zoom="0.70710678"
     inkscape:cx="458.20519"
     inkscape:cy="501.33871"
     inkscape:window-width="1920"
     inkscape:window-height="1009"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="Layer_1"
     showguides="true" /><defs
     id="defs1"><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect32"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.7797334,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.8132809,0,1 @ F,0,0,1,0,5.8014702,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect30"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.4311784,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect28"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,5.4311536,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect22"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.7797334,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.8132809,0,1 @ F,0,0,1,0,5.8014702,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect21"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.4311784,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect20"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,5.4311536,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect18"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,9.8403439,0,1 @ F,0,0,1,0,8.8956607,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,8.2491195,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 | F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,9.2469059,0,1 | F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 | F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,11.915831,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect17"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><style
       id="style1">
      .cls-1 {
        fill: #1e1e1e;
      }

      .cls-1, .cls-2, .cls-3, .cls-4, .cls-5 {
        stroke-width: 0px;
      }

      .cls-2 {
        fill: #34c85a;
      }

      .cls-6 {
        fill: #fff;
        font-family: Poppins-Bold, Poppins;
        font-size: 168px;
        font-weight: 700;
      }

      .cls-3 {
        fill: #ff2c55;
      }

      .cls-4 {
        fill: #af53de;
      }

      .cls-5 {
        fill: #2fb0c6;
      }
    </style><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect28-9"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,5.4311536,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect30-4"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.4311784,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /><inkscape:path-effect
       effect="fillet_chamfer"
       id="path-effect32-8"
       is_visible="true"
       lpeversion="1"
       nodesatellites_param="F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.7797334,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,5.8132809,0,1 @ F,0,0,1,0,5.8014702,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1 @ F,0,0,1,0,0,0,1"
       radius="0"
       unit="px"
       method="auto"
       mode="F"
       chamfer_steps="1"
       flexible="false"
       use_knot_distance="true"
       apply_no_radius="true"
       apply_with_radius="true"
       only_selected="false"
       hide_knots="false" /></defs><g
     id="g3"
     transform="matrix(4.1550393,0,0,4.1550393,-846.42433,-1426.026)"
     inkscape:label="Consept"><path
       id="polygon1"
       class="cls-4"
       style="fill:#af53de;stroke-width:0px"
       d="m 348.09,530.47 h -48.77 l -10e-6,-66.94018 48.77,-17.75083 z"
       sodipodi:nodetypes="ccccc" /><path
       id="polygon2"
       class="cls-3"
       transform="translate(0.01440889,0.09998783)"
       style="color:#000000;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-size:medium;line-height:normal;font-family:sans-serif;font-variant-ligatures:normal;font-variant-position:normal;font-variant-caps:normal;font-variant-numeric:normal;font-variant-alternates:normal;font-variant-east-asian:normal;font-feature-settings:normal;font-variation-settings:normal;text-indent:0;text-align:start;text-decoration-line:none;text-decoration-style:solid;text-decoration-color:#000000;letter-spacing:normal;word-spacing:normal;text-transform:none;writing-mode:lr-tb;direction:ltr;text-orientation:mixed;dominant-baseline:auto;baseline-shift:baseline;text-anchor:start;white-space:normal;shape-padding:0;shape-margin:0;inline-size:0;clip-rule:nonzero;display:inline;overflow:visible;visibility:visible;isolation:auto;mix-blend-mode:normal;color-interpolation:sRGB;color-interpolation-filters:linearRGB;solid-color:#000000;solid-opacity:1;vector-effect:none;fill:#ff2c55;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-dasharray:none;stroke-dashoffset:0;stroke-opacity:1;-inkscape-stroke:none;color-rendering:auto;image-rendering:auto;shape-rendering:auto;text-rendering:auto;enable-background:accumulate;stop-color:#000000;stop-opacity:1"
       d="m 250.4,524.93885 0,-43.70885 46.19,-16.81178 V 530.37 H 255.83115 A 5.4311536,5.4311536 45 0 1 250.4,524.93885 Z"
       sodipodi:nodetypes="ccccc"
       inkscape:path-effect="#path-effect20"
       inkscape:original-d="m 250.4,530.37 v -49.14 l 46.19,-16.81178 V 530.37 Z" /><path
       id="polygon3"
       class="cls-5"
       transform="translate(-0.14441751,-0.31001115)"
       style="fill:#2fb0c6;stroke-width:0px"
       d="M 350.94999,415.68001 400,397.82727 v 127.52156 a 5.4311784,5.4311784 135 0 1 -5.43118,5.43118 h -43.61883 z"
       sodipodi:nodetypes="ccccc"
       inkscape:path-effect="#path-effect21"
       inkscape:original-d="M 350.94999,415.68001 400,397.82727 v 132.95274 h -49.05001 z" /><path
       id="polyline3"
       class="cls-2"
       style="fill:#34c85a;stroke-width:0px"
       d="m 250.4144,478.43984 v -38.69355 a 8.2543149,8.2543149 125 0 1 5.43117,-7.75652 l 43.47442,-15.82339 v -20.685 a 8.3022254,8.3022254 125 0 1 5.4627,-7.80154 l 89.62129,-32.61948 a 4.0622333,4.0622333 35.000001 0 1 5.4516,3.81725 v 35.75 l -51.76559,18.84113 v 29.42009"
       sodipodi:nodetypes="cccccccc"
       inkscape:path-effect="#path-effect22"
       inkscape:original-d="m 250.4144,478.43984 v -44.47328 l 48.90559,-17.80018 V 389.6681 l 100.53559,-36.59196 v 41.55147 l -51.76559,18.84113 v 29.42009" /><path
       d="m 238.85659,863.39638 h -22.78348 v -74.03312 h 22.78348 z m 0.63287,-84.58103 h -24.04922 v -17.99736 h 24.04922 z m 92.27306,84.58103 h -22.91004 v -36.71991 q 0,-4.48285 -0.44301,-8.8998 -0.44302,-4.48285 -1.5189,-6.59243 -1.26575,-2.43921 -3.73396,-3.55992 -2.40492,-1.12072 -6.77175,-1.12072 -3.10109,0 -6.32874,1.05479 -3.16437,1.05479 -6.89834,3.36214 v 52.47585 h -22.78347 v -74.03312 h 22.78347 v 8.17463 q 6.07559,-4.94434 11.64488,-7.58132 5.63259,-2.63698 12.46763,-2.63698 11.51831,0 17.97363,6.988 6.5186,6.98798 6.5186,20.89802 z m 110.18341,0 h -29.80837 l -28.16291,-39.29097 -5.69587,7.18577 v 32.1052 h -24.30237 v -98.16145 h 24.30237 v 44.43305 l 33.66892,-44.43305 h 28.16289 l -36.83327,45.42192 z m 62.21154,-51.42106 h -2.0252 q -1.4556,-0.52738 -4.68326,-0.79108 -3.22767,-0.26371 -5.37944,-0.26371 -4.87313,0 -8.60709,0.65925 -3.73395,0.65924 -8.0375,2.24144 v 49.57516 h -22.78347 v -74.03312 h 22.78347 v 10.87752 q 7.5312,-6.72428 13.10049,-8.89978 5.56929,-2.24144 10.25257,-2.24144 1.20247,0 2.72136,0.0659 1.5189,0.0659 2.65807,0.19777 z m 82.90653,18.78847 h -52.14884 q 0.5063,8.70201 6.32875,13.31672 5.88572,4.61472 17.27745,4.61472 7.21477,0 13.98653,-2.70295 6.77175,-2.70286 10.69557,-5.80131 h 2.53151 v 19.05212 q -7.72107,3.23037 -14.55612,4.68062 -6.83504,1.45042 -15.12569,1.45042 -21.39116,0 -32.78288,-10.02054 -11.39175,-10.02052 -11.39175,-28.54527 0,-18.32697 10.75886,-29.00673 10.82216,-10.74569 29.61853,-10.74569 17.34075,0 26.07441,9.16349 8.73367,9.09758 8.73367,26.23792 z m -22.6569,-13.91006 q -0.18986,-7.44945 -3.5441,-11.20715 -3.35423,-3.75769 -10.44242,-3.75769 -6.5819,0 -10.82215,3.55992 -4.24026,3.55992 -4.74655,11.40492 z m 107.84178,46.54265 h -22.78349 v -7.71317 q -5.88575,5.01022 -11.012,7.38349 -5.12627,2.37333 -11.83475,2.37333 -12.97392,0 -20.75828,-10.41605 -7.78435,-10.41608 -7.78435,-28.08381 0,-9.42719 2.59479,-16.67887 2.65807,-7.31761 7.21476,-12.52564 4.30355,-4.94433 10.44242,-7.64722 6.13889,-2.76885 12.27777,-2.76885 6.39204,0 10.44243,1.45035 4.11367,1.38442 8.41721,3.55992 v -31.51187 h 22.78349 z m -22.78349,-20.43659 v -36.32435 q -2.40487,-1.05478 -5.06301,-1.51625 -2.65807,-0.46147 -4.87313,-0.46147 -8.98679,0 -13.4802,5.86727 -4.49341,5.80134 -4.49341,16.15148 0,10.87753 3.60739,15.82185 3.60738,4.87838 11.58163,4.87838 3.10106,0 6.58185,-1.18663 3.4808,-1.25254 6.13888,-3.23028 z m 116.95523,-16.54701 q 0,18.26105 -10.25266,28.80892 -10.18921,10.48204 -28.66916,10.48204 -18.47994,0 -28.73245,-10.48204 -10.18929,-10.54787 -10.18929,-28.80892 0,-18.39291 10.25252,-28.87489 10.31589,-10.48199 28.66922,-10.48199 18.60657,0 28.73254,10.54791 10.18928,10.5479 10.18928,28.80897 z m -28.03638,17.99736 q 2.21506,-2.83476 3.29099,-6.79022 1.13915,-4.0214 1.13915,-11.0753 0,-6.52651 -1.13915,-10.94345 -1.13923,-4.41693 -3.16438,-7.0539 -2.02522,-2.70291 -4.87314,-3.82363 -2.848,-1.12072 -6.13891,-1.12072 -3.29099,0 -5.88567,0.92294 -2.53153,0.92295 -4.87314,3.69178 -2.08853,2.57104 -3.3543,7.0539 -1.20246,4.48287 -1.20246,11.27308 0,6.06504 1.07584,10.54791 1.076,4.41693 3.16446,7.11982 2.02514,2.57104 4.80983,3.75767 2.84791,1.18671 6.45537,1.18671 3.10098,0 5.88567,-1.05481 2.84792,-1.12073 4.80984,-3.69178 z"
       id="text4"
       style="font-weight:700;font-size:168px;font-family:Poppins-Bold, Poppins;fill:#f2f2f2;fill-opacity:1;stroke-width:0.771503"
       class="cls-6"
       aria-label="inKredo"
       transform="matrix(0.27155847,0,0,0.27155847,191.72758,336.25491)" /></g></svg>
