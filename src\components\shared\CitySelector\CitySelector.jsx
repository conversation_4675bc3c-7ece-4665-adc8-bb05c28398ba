import { Autocomplete, CircularProgress, TextField } from "@mui/material";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

import { clearSelectedCity, setSelectedCity } from "@/redux/features/citySlice";
import classes from "@/components/Dashboard/Sales/UserForm/userform.module.css";
import { useSearchParams } from "react-router-dom";
import { Controller, useFormContext } from "react-hook-form";
import { useGetAllCitiesQuery } from "@/redux/services/userApiSlice";
import { searchDataRefractorer } from "@/util/formatters";

function CitySelector({ user = {}, isSearch, width = "", isLabelAvailableForControlled = false, customOnChange }) {
  const dispatch = useDispatch();
  const [searchParams, setSearchParams] = useSearchParams();

  const formContext = useFormContext();

  const errors = formContext?.formState?.errors || {};
  const control = formContext?.control || null;

  const selectedCity = useSelector((state) => state.city.selectedCity);
  const { data: cities, isLoading } = useGetAllCitiesQuery();

  console.log(user);

  useEffect(() => {
    if (!isSearch || !searchParams.get("customer__city")) {
      dispatch(clearSelectedCity());
    }
  }, [dispatch, isSearch, searchParams]);

  useEffect(() => {
    if (user.city && cities) {
      const foundCity = cities.results.find(city => city.id == user.city);
      dispatch(setSelectedCity(foundCity));
    }
    else if (isSearch && cities && searchParams.get("customer__city")) {
      const cityId = searchParams.get("customer__city");
      const foundCity = cities.results.find(city => city.id == cityId);
      if (foundCity) {
        dispatch(setSelectedCity(foundCity));
      }
    }
  }, [cities, dispatch, user.city, isSearch, searchParams]);

  function handleChange(newValue) {
    if (isSearch && customOnChange) {
      customOnChange(newValue);
    }
    if (isSearch) {
      const current = Object.fromEntries(searchParams.entries());
      current.customer__city = newValue ? newValue.id : null;
      current['offset'] = 0;
      setSearchParams(searchDataRefractorer(current));
    }
    dispatch(setSelectedCity(newValue || null));
  }

  return control ? (
    <Controller
      name="city"
      control={control}
      defaultValue={user.city || null}
      render={({ field }) => (
        <Autocomplete
          id="citySelector"
          size="small"
          className={`${width} ${classes.citySelectorUser}`}
          clearOnEscape
          options={cities?.results || []}
          getOptionLabel={(option) => option?.name || ""}
          loading={isLoading}
          isOptionEqualToValue={(option, value) => option?.id === value?.id}
          onChange={(_, newValue) => {
            field.onChange(newValue ? newValue.id : null);
            handleChange(newValue);
          }}
          value={selectedCity || null}
          renderInput={(params) => (
            <TextField
              {...params}
              label={isLabelAvailableForControlled ? "Şəhər adı seç" : ""}
              placeholder={!isLabelAvailableForControlled ? "Şəhər adı seç" : ""}
              variant="standard"
              error={!isSearch && !!errors?.city}
              helperText={!isSearch && errors?.city?.message}
              InputProps={{
                ...params.InputProps,
                endAdornment: (
                  <>
                    {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                    {params.InputProps.endAdornment}
                  </>
                ),
              }}
            />
          )}
        />
      )}
    />
  ) : (
    <Autocomplete
      id="citySelector"
      size="small"
      className={`${width} ${classes.citySelectorUser}`}
      clearOnEscape
      options={cities?.results || []}
      getOptionLabel={(option) => option?.name || ""}
      loading={isLoading}
      isOptionEqualToValue={(option, value) => option?.id === value?.id}
      onChange={(_, newValue) => handleChange(newValue)}
      value={selectedCity || null}
      renderInput={(params) => (
        <TextField
          {...params}
          label="Şəhər adı seç"
          variant="standard"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {isLoading ? <CircularProgress color="inherit" size={20} /> : null}
                {params.InputProps.endAdornment}
              </>
            ),
          }}
        />
      )}
    />
  );
}

export default CitySelector;
