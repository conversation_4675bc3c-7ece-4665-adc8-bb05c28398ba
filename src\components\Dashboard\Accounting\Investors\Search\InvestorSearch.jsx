import SearchComponent from "@/components/shared/Search/SearchComponent";

function InvestorsSearch() {
  const searchFields = [
    {
      type: 'text',
      name: 'user_fullname',
      label: 'İnvestor',
      fieldProps: { className: 'my-[30px]' }
    }
  ];

  const defaultValues = {
    user_fullname: ''
  };

  return (
    <SearchComponent
      fields={searchFields}
      defaultValues={defaultValues}
      limit="13"
    />
  );
}

export default InvestorsSearch;
