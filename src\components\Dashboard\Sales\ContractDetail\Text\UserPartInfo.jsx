import { Link } from "react-router-dom"
import { Text<PERSON><PERSON>, Tooltip } from "@mui/material"
import classes from "./contractdetailtext.module.css"
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper"
import { useSelector } from "react-redux"
import CitySelector from "@/components/shared/CitySelector/CitySelector"

function getDefaultData(id, fullname, phone1) {
    return {
        id,
        fullname,
        phone1
    }
}

function UserPartInfo({ contractDetail, isEdit }) {
    const guarantorDefault = getDefaultData(contractDetail?.guarantor?.id, contractDetail?.guarantor?.fullname, contractDetail?.guarantor?.phone1);
    const executorDefault = getDefaultData(contractDetail?.executor?.id, contractDetail?.executor?.fullname, contractDetail?.executor?.phone1);
    const attorneyDefault = getDefaultData(contractDetail?.attorney?.id, contractDetail?.attorney?.fullname, contractDetail?.attorney?.phone1);
    const customerDefault = getDefaultData(contractDetail?.customer?.id, contractDetail?.customer?.fullname, contractDetail?.customer?.phone1);

    const { guarantor: guarantorState, executor: executorState, attorney: attorneyState, customer: customerState } = useSelector(state => state.infiniteScroll);

    console.log(customerState, guarantorState);

    const defaultCity = {
        city: contractDetail?.city?.id
    }

    return (
        <div className="flex-grow flex-shrink basis-[200px]">

            <div className="flex justify-between mb-[10px]">
                <p className={`w-1/2 ${classes.contractTextLabel}`}>Müqavilə tarixi:</p>
                <p className={`${classes.contractTextValue}`}>{contractDetail.contract_date}</p>
            </div>

            {isEdit ? (
                <AutocompleteWrapper type="customer" label="Müştəri *" defaultValue={customerDefault}>
                    <div className="flex items-center justify-between my-[14px]">
                        <span className="w-1/3 text-[14px]">Müştəri tel:</span>
                        <TextField
                            className="w-2/3"
                            variant="standard"
                            disabled
                            value={customerState?.selectedValue?.phone1 || ""}
                        />
                    </div>
                </AutocompleteWrapper>
            ) : (
                <>
                    <div className="flex justify-between mb-[10px]">
                        <p className={`w-1/2 ${classes.contractTextLabel}`}>Müştəri:</p>
                        <Tooltip title={customerDefault?.fullname} arrow>
                            <Link to={customerDefault?.id ? `../users/${customerDefault?.id}` : '#'} className={`${classes.contractTextValue} blueText cursor-pointer`}>
                                {customerDefault?.fullname}
                            </Link>
                        </Tooltip>
                    </div>
                    <div className="flex justify-between mb-[10px]">
                        <p className={`w-1/2 ${classes.contractTextLabel}`}>Müştəri tel:</p>
                        <a href={`https://wa.me/${customerDefault?.phone1}`} target="_blank" rel="noopener noreferrer" className={`${classes.contractTextValue}`}>
                            {customerDefault?.phone1}
                        </a>
                    </div>
                </>
            )}

            {isEdit ? (
                <AutocompleteWrapper type="guarantor" label="Kəfil" defaultValue={guarantorDefault}>
                    <div className="flex items-center justify-between my-[14px]">
                        <span className="w-1/3 text-[14px]">Kəfil tel:</span>
                        <TextField
                            className="w-2/3"
                            variant="standard"
                            disabled
                            value={guarantorState?.selectedValue?.phone1 || ""}
                        />
                    </div>
                </AutocompleteWrapper>
            ) : (
                <>
                    <div className="flex justify-between mb-[10px]">
                        <p className={`w-1/2 ${classes.contractTextLabel}`}>Kəfil:</p>
                        <Tooltip title={guarantorDefault?.fullname} arrow>
                            <Link to={guarantorDefault?.id ? `../users/${guarantorDefault?.id}` : '#'} className={`${classes.contractTextValue} blueText cursor-pointer`}>
                                {guarantorDefault?.fullname}
                            </Link>
                        </Tooltip>
                    </div>
                    <div className="flex justify-between mb-[10px]">
                        <p className={`w-1/2 ${classes.contractTextLabel}`}>Kəfil tel:</p>
                        <a href={`https://wa.me/${guarantorDefault?.phone1}`} target="_blank" rel="noopener noreferrer" className={`${classes.contractTextValue}`}>
                            {guarantorDefault?.phone1}
                        </a>
                    </div>
                </>
            )}

            {isEdit ? (
                <AutocompleteWrapper type="executor" label="İcraçı" defaultValue={executorDefault}>
                    <div className="flex items-center justify-between my-[14px]">
                        <span className="w-1/3 text-[14px]">İcraçı tel:</span>
                        <TextField
                            className="w-2/3"
                            variant="standard"
                            disabled
                            value={executorState?.selectedValue?.phone1 || ""}
                        />
                    </div>
                </AutocompleteWrapper>
            ) : (
                <div className="flex justify-between mb-[10px]">
                    <p className={`w-1/2 ${classes.contractTextLabel}`}>İcraçı:</p>
                    <Tooltip title={executorDefault?.fullname} arrow>
                        <Link to={`../users/${executorDefault?.id}`} className={`${classes.contractTextValue} blueText`}>
                            {executorDefault?.fullname}
                        </Link>
                    </Tooltip>
                </div>
            )}

            {isEdit ? (
                <AutocompleteWrapper type="attorney" label="Vəkil" defaultValue={attorneyDefault}>
                    <div className="flex items-center justify-between my-[14px]">
                        <span className="w-1/3 text-[14px]">Vəkil tel:</span>
                        <TextField
                            className="w-2/3"
                            variant="standard"
                            disabled
                            value={attorneyState?.selectedValue?.phone1 || ""}
                        />
                    </div>
                </AutocompleteWrapper>
            ) : (
                <div className="flex justify-between mb-[10px]">
                    <p className={`w-1/2 ${classes.contractTextLabel}`}>Vəkil:</p>
                    <Tooltip title={attorneyDefault?.fullname} arrow>
                        <Link to={`../users/${attorneyDefault?.id}`} className={`${classes.contractTextValue} blueText`}>
                            {attorneyDefault?.fullname}
                        </Link>
                    </Tooltip>
                </div>
            )}

            <div className="flex justify-between items-center mb-[10px]">
                <p className={`${isEdit ? "" : "w-1/2"} ${classes.contractTextLabel}`}>Şəhər:</p>
                {isEdit ?
                    <div className="w-2/3">
                        <CitySelector user={defaultCity} width="w-full" isSearch={false} />
                    </div>
                    : <div className="w-1/2">
                        <p className={`${classes.contractTextValue}`}>{contractDetail?.city?.name}</p>
                    </div>
                }
            </div>

        </div>
    );
}

export default UserPartInfo;