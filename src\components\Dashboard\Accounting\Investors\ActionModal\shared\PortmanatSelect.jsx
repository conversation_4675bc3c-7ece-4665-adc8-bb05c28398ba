import { FormControl, MenuItem, Select } from "@mui/material";

function PortmanatSelect({
    portmanat,
    setPortmanat,
    selectedValue,
    userPortmanats,
    allPortmanats = [],
    initialPortmanat,
    type,
}) {
    const isSource = type === "source";
    const isTransferModal = Object.prototype.hasOwnProperty.call(portmanat, "target");

    function handleSelectChange(e) {
        const { value } = e.target;
        const foundPortmanat = userPortmanats?.results.find(portmanat => portmanat.id == value);

        if (isTransferModal) {
            setPortmanat({ ...portmanat, [type]: foundPortmanat || initialPortmanat });
        } else {
            setPortmanat({ source: foundPortmanat || initialPortmanat });
        }
    }

    const filteredPortmanats = isTransferModal && portmanat.target && portmanat.source ?
        allPortmanats.filter(p => p.id !== portmanat[isSource ? "target" : "source"]?.id) :
        allPortmanats;

    const name = isTransferModal ? (isSource ? "from_wallet" : "to_wallet") : "wallet";
    const currentValue = isTransferModal ? portmanat[type]?.id : portmanat.source?.id;

    return (
        <div className="w-2/3 flex items-center">
            <FormControl variant="standard" sx={{ width: "100%", alignItems: "end" }}>
                <Select
                    className="w-full"
                    value={currentValue || ""}
                    onChange={handleSelectChange}
                    disabled={!selectedValue}
                    name={name}
                    sx={{ '& .MuiSelect-select': { fontSize: '14px' } }}
                >
                    {filteredPortmanats.map((portmanat, index) => (
                        <MenuItem key={index} value={portmanat.id}>
                            {portmanat.name} - {portmanat.balance} AZN
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
            {((isTransferModal && portmanat[type]?.id) || (!isTransferModal && portmanat.source?.id)) && (
                <button
                    type="button"
                    className="ml-2 text-gray-500 hover:text-gray-700"
                    onClick={() => {
                        if (isTransferModal) {
                            setPortmanat({ ...portmanat, [type]: initialPortmanat });
                        } else {
                            setPortmanat({ source: initialPortmanat });
                        }
                    }}
                >
                    ✕
                </button>
            )}
        </div>
    )
}

export default PortmanatSelect