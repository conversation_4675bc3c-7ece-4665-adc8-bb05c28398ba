import openCloseLogo from "@/assets/openCloseLogo.svg"

import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

import UsersSearch from "@/components/Dashboard/Sales/Users/<USER>/UsersSearch";
import ContractsSearch from "@/components/Dashboard/Sales/Contracts/Search/ContractsSearch";
import InvestorsSearch from "@/components/Dashboard/Accounting/Investors/Search/InvestorSearch";
import OperationSearch from "@/components/Dashboard/Accounting/OperationHistory/Search/OpsSearch";

import classes from "./searchlayout.module.css";
import { useDispatch, useSelector } from "react-redux";
import { handleSearchOpen } from "@/redux/features/layout/layoutSlice";
import InstallmentSearch from "../../Sales/Installments/Search/InstallmentSearch";

const searchComponents = {
    "/sales/users": <UsersSearch />,
    "/sales/administrators": <UsersSearch />,
    "/sales/contracts": <ContractsSearch />,
    "/sales/installments": <InstallmentSearch />,
    "/accounting/investors": <InvestorsSearch />,
    "/accounting/ops": <OperationSearch />,
};

function SearchLayout() {
    const location = useLocation();
    const [searchComponent, setSearchComponent] = useState(null);

    const dispatch = useDispatch();
    const { isSearchOpen } = useSelector(state => state.layout);

    useEffect(() => {
        const matchedPath = Object.keys(searchComponents).find(path => location.pathname == path);
        setSearchComponent(matchedPath ? searchComponents[matchedPath] : null);
    }, [location.pathname]);

    return (
        <>
            {(searchComponent) &&
                <div className={`${classes.search} ${isSearchOpen && classes.searchOpened}`}>
                    <div>
                        {isSearchOpen &&
                            <>
                                <h2 className="text-center font-bold text-[22px] whitespace-nowrap">Ətraflı axtar</h2>
                                {searchComponent}
                            </>
                        }
                        <button
                            onClick={() => dispatch(handleSearchOpen())}
                            className={`transition-rotate duration-300 absolute top-5 left-5 ${isSearchOpen ? "rotate-180" : ""}`}
                        >
                            <img src={openCloseLogo} />
                        </button>
                    </div>
                </div>
            }
        </>
    );
}

export default SearchLayout;
