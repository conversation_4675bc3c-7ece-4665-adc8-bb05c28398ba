import { createApi } from "@reduxjs/toolkit/query/react";
import { authBaseQueryWithReauth } from "./baseQueryWithReauth";

export const accountingApiSlice = createApi({
  baseQuery: authBaseQueryWithReauth,
  reducerPath: "accountingApiSlice",
  tagTypes: ["Investors", "Ops", "Balance"],
  endpoints: (builder) => ({
    getAllInvestors: builder.query({
      query: (params) => ({
        url: "/accounting/investors-balance/",
        method: "GET",
        params,
      }),
      providesTags: ["Investors"],
      keepUnusedDataFor: 1,
    }),
    incomeBalance: builder.mutation({
      query: (credentials) => ({
        url: "/accounting/income-investor-balance/",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Investors"],
    }),
    expenseBalance: builder.mutation({
      query: (credentials) => ({
        url: "/accounting/expense-investor-balance/",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Investors"],
    }),
    getAllOps: builder.query({
      query: (params) => ({
        url: "/operation-histories/",
        method: "GET",
        params,
      }),
      providesTags: ["Ops"],
      keepUnusedDataFor: 0,
    }),
    getAllBalance: builder.query({
      query: (params) => ({
        url: "/accounting/company-balance/",
        method: "GET",
        params,
      }),
      providesTags: ["Balance"],
      keepUnusedDataFor: 1,
    }),
    updateBalance: builder.mutation({
      query: (credentials) => ({
        url: "/accounting/change-liabilities/",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Balance"],
    }),
    getPortmanat: builder.query({
      query: (params) => ({
        url: "/accounting/wallets/",
        method: "GET",
        params,
      }),
      keepUnusedDataFor: 0,
    }),
    addPortmanat: builder.mutation({
      query: (credentials) => ({
        url: "/accounting/create-wallet/",
        method: "POST",
        body: credentials,
      }),
    }),
    transferPortmanat: builder.mutation({
      query: (credentials) => ({
        url: "/accounting/wallets/transfer/",
        method: "POST",
        body: credentials,
      }),
    }),
  }),
});

export const {
  useGetAllInvestorsQuery,
  useIncomeBalanceMutation,
  useExpenseBalanceMutation,
  useGetAllOpsQuery,
  useGetAllBalanceQuery,
  useUpdateBalanceMutation,
  useGetPortmanatQuery,
  useAddPortmanatMutation,
  useTransferPortmanatMutation,
} = accountingApiSlice;
