import { DRAWER_WIDTHS } from "@/util/constants";
import { createSlice } from "@reduxjs/toolkit";

const {COLLAPSED_WIDTH, EXPANDED_WIDTH} = DRAWER_WIDTHS;

const initialState = {
  mobileOpen: false,
  isClosing: false,
  drawerWidth: COLLAPSED_WIDTH,
  isSearchOpen: window.innerWidth > 992,
};

const layoutSlice = createSlice({
  name: "layout",
  initialState,
  reducers: {
    handleDrawerWidth: (state) => {
      state.drawerWidth =
        state.drawerWidth === EXPANDED_WIDTH
          ? COLLAPSED_WIDTH
          : EXPANDED_WIDTH;
    },
    handleDrawerClose: (state) => {
      state.isClosing = true;
      state.mobileOpen = false;
    },
    handleDrawerTransitionEnd: (state) => {
      state.isClosing = false;
    },
    handleDrawerToggle: (state) => {
      if (!state.isClosing) {
        state.mobileOpen = !state.mobileOpen;
        if (state.mobileOpen) {
          state.drawerWidth = EXPANDED_WIDTH;
        }
      }
    },
    handleSearchOpen: (state) => {
      state.isSearchOpen = !state.isSearchOpen;
    },
  },
});

export const {
  handleDrawerWidth,
  handleDrawerClose,
  handleDrawerTransitionEnd,
  handleDrawerToggle,
  handleSearchOpen
} = layoutSlice.actions;

export default layoutSlice.reducer;
