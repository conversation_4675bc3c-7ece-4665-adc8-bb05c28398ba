const DateRangePicker = ({ fromDate, toDate, onFromDateChange, onToDateChange }) => {
  return (
    <div className="flex items-center gap-2">
      <div className="flex flex-col">
        <label className="text-xs text-gray-600">From</label>
        <input
          type="date"
          className="p-1 border border-gray-300 rounded-lg text-xs"
          value={fromDate}
          onChange={(e) => onFromDateChange(e.target.value)}
        />
      </div>
      <div className="flex flex-col">
        <label className="text-xs text-gray-600">To</label>
        <input
          type="date"
          className="p-1 border border-gray-300 rounded-lg text-xs"
          value={toDate}
          onChange={(e) => onToDateChange(e.target.value)}
        />
      </div>
    </div>
  );
};

export default DateRangePicker;