import { useState, useEffect } from "react";
import { useGetAllUsersQuery } from "@/redux/services/userApiSlice";

const PAGE_SIZE = 41; // Fetching a bit more to determine if there are more results

export function usePaginatedUsers(shouldFetch, searchQuery, additionalParams = {}) {
  const [offset, setOffset] = useState(0);
  const [users, setUsers] = useState([]);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    setOffset(0);
    setUsers([]);
    setHasMore(false);
  }, [searchQuery]);

  const params = {
    limit: PAGE_SIZE,
    offset,
    fullname: searchQuery,
    ...additionalParams
  };

  const { data, isLoading, isFetching } = useGetAllUsersQuery(params, {
    skip: !shouldFetch,
  });

  useEffect(() => {
    if (data?.results && !isFetching) {
      setUsers(prev => {
        const newUsers = data.results;
        return offset === 0 ? newUsers : [...prev, ...newUsers];
      });
      setHasMore(data.results.length === PAGE_SIZE);
    }
  }, [data, offset, isFetching]);

  const loadMore = () => {
    if (!isFetching && hasMore) {
      setOffset(prev => prev + PAGE_SIZE);
    }
  };

  const reset = () => {
    setOffset(0);
    setUsers([]);
    setHasMore(false);
  };

  return {
    users,
    isLoading: isLoading || isFetching,
    isFetching,
    hasMore,
    loadMore,
    reset
  };
}
