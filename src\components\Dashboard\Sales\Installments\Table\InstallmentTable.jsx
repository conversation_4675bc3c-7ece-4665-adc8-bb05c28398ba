import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useSelector } from "react-redux";
import { useSearchParams } from "react-router-dom";
import TableHead from "./TableHead";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent";

import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";
import TableData from "./TableData";
import PaymentModal from "../../../../shared/PaymentModal/PaymentModal";
import { useGetAllInstallmentsQuery } from "@/redux/services/contractApiSlice";
import { roundTo2 } from "@/util/formatters";

function InstallmentTable() {
    const [searchParams] = useSearchParams();

    const limit = parseInt(searchParams.get("limit") || "16", 10);
    const offset = parseInt(searchParams.get("offset") || "0", 10);

    const queryObject = {
        ...Object.fromEntries(searchParams.entries()),
        limit,
        offset,
    };

    const { data: installments, isLoading, isError, error, refetch } = useGetAllInstallmentsQuery(queryObject);

    const { isSearchOpen } = useSelector(state => state.layout);
    const { isModalOpen, contractInstallment, params } = useSelector(state => state.contractPayment);


    if (isError && error.originalStatus == 404) return <p className="text-center my-[40px]">Məlumat tapılmadı</p>
    if (isError) return <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>

    return (
        <div className="pb-8 pt-[24px] w-full">
            <div className={`${generalTableStyles.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened}`}>
                {isLoading ? (
                    <ContentLoadingScreen />
                ) : (
                    installments
                        && installments.results
                        && installments.results.data
                        && installments.results.data.length > 0 ?
                        <>
                            <PaymentModal
                                isOpen={isModalOpen}
                                contractInstallment={contractInstallment}
                                refetch={refetch}
                                params={params}
                            />
                            <table className={generalTableStyles.table}>
                                <TableHead />
                                <tbody className={generalTableStyles.tbody}>
                                    {installments.results.data.map(installment => <TableData key={installment.id} installment={installment} />)}
                                </tbody>
                                <tfoot className={generalTableStyles.tfoot}>
                                    <tr>
                                        <td>Sıralanır: {installments.count}</td>
                                        <td colSpan={4}></td>
                                        <td>{roundTo2(Number(installments?.results?.installment_summaries?.total_remaining_debt ?? 0))}</td>
                                        <td colSpan={2}></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </>
                        : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
                )}
            </div>
            {installments
                && !isLoading
                && <PaginationComponent totalCount={installments.count} />}
        </div>
    )
}

export default InstallmentTable