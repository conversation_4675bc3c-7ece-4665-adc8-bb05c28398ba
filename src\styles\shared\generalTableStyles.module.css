@import "../App.css";

.tableWrapper {
  width: calc(100% - 70px);
  overflow: auto;
  border-radius: 20px;
  height: calc(100vh - 220px);
  transition: width 0.3s ease;
}

.table {
  width: 100%;
  border: 2px solid var(--table-border-color);
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 20px;
}

.thead {
  position: sticky;
  top: 0;
  background-color: var(--secondary-color);
  border-radius: 20px;

  th {
    padding: 12px 8px;
    text-align: left;
    font-size: 14px;
    font-weight: 400;
    color: white;
    /* line-height: 16px; */
    white-space: nowrap;
  }

  th:nth-child(1) {
    border-top-left-radius: 18px;
    padding-left: 13px;
  }

  th:last-child {
    border-top-right-radius: 18px;
    padding-right: 13px;
  }
}

.tbody {
  td {
    padding: 10px 8px;
    border-bottom: 2px solid var(--table-border-color);
    /* line-height: 20px; */
    font-size: 14px;
    white-space: nowrap;
  }

  tr:last-child {
    td {
      border-bottom: none;
    }
  }

  tr {
    td:first-child {
      padding-left: 13px;
    }

    td:last-child {
      padding-right: 13px;
    }
  }

  /* td:last-child {
    border-bottom-right-radius: 18px;
    border-bottom-left-radius: 18px;
  } */

  tr {
    border-top: 2px solid var(--table-border-color);
    border-bottom: 2px solid var(--table-border-color);
  }
}

.tfoot {
  position: sticky;
  bottom: 0;
  height: 40px;
  background-color: var(--secondary-color);

  td {
    padding: 10px 8px;
    line-height: 20px;
    font-size: 14px;
    white-space: nowrap;
    color: white;
  }

  td:nth-child(1) {
    border-bottom-left-radius: 18px;
    padding-left: 13px;
  }

  td:last-child {
    border-bottom-right-radius: 18px;
  }
}

.completepayment {
  background-color: var(--fullpayment-color);
}

.incompletepayment {
  background-color: var(--incompletepayment-color);
}

.latepayment {
  background-color: var(--latepayment-color);
}

.overdue {
  background-color: white;
}

.allPaidContracts {
  background-color: #EEB0FA;
}

.allPaidInstallments {
  background-color: gray;
}

.overPayment {
  background-color: #BDCDF8;
}

.tdStyle {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 300px;
}

.searchOpened {
  width: calc(100% - 80px);
}

@media screen and (min-width: 600px) {
  .tableWrapper {
    max-width: initial;
    width: calc(100% - 90px);
    margin: initial;
    transition: width 0.3s ease;
  }

  .searchOpened {
    width: calc(100% - 320px);
  }
}

.detailTable {
  padding-top: 0;
  width: 100%;
  max-height: 375px;
}

.noteTextStyle {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100px;
}

.title {
  margin-bottom: 19px;
  font-weight: bold;
  font-size: 24px;
}