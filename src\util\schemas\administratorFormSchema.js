import * as yup from "yup";
import { emailRegex } from "./profileSchema";

const phoneRegex = /^\d{2}\d{3}\d{2}\d{2}$/;

export const administratorFormSchema = yup.object().shape({
  fullname: yup.string().required("Ad Soyad Ata adı daxil edin"),
  phone1: yup
    .string()
    .matches(
      phoneRegex,
      "Nömrə doğru strukturda deyil. Düzgün nümunə: +994501234567"
    )
    .required("Nömrə daxil edin"),
  phone2: yup
    .string()
    .test(
      "valid-phone",
      "Nömrə doğru strukturda deyil. Düzgün nümunə: +994501234567",
      (value) => {
        if (!value) return true;
        return phoneRegex.test(value);
      }
    ),

  email: yup
    .string()
    .test("valid-email", "E-mail düzgün formatda deyil", (value) => {
      return emailRegex.test(value);
    }),

  profileImage: yup.mixed().test("file-required", "Şəkil seçin", (value) => {
    return value instanceof File || (!!value && typeof value === "object");
  }),
});
