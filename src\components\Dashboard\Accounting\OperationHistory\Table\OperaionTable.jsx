import { useSearchParams } from "react-router-dom";

import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";
import TableData from "./TableData.jsx";
import TableHeader from "./TableHeader.jsx";
import { useSelector } from "react-redux";
import { useGetAllOpsQuery } from "@/redux/services/accountingApiSlice.js";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent.jsx";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";

function OperationTable() {
  const [searchParams] = useSearchParams();

  const limit = searchParams.get("limit") || "16";
  const offset = searchParams.get("offset") || "0";

  const queryObject = {
    limit,
    offset,
    ...Object.fromEntries(searchParams.entries()),
  };

  const { data: ops, isLoading,  isError, error } = useGetAllOpsQuery(queryObject);

  const { isSearchOpen } = useSelector(state => state.layout);

  if (isError) return (
    <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>
  )

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className={`${generalTableStyles.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened}`}>
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          ops
            && ops.results
            && ops.results.data
            && ops.results.data.length > 0
            ? <table className={generalTableStyles.table}>
              <TableHeader />
              <tbody className={generalTableStyles.tbody}>
                {ops &&
                  ops.results.data.map((op) => (
                    <TableData key={op.id} data={op} />
                  ))}
              </tbody>
              <tfoot className={generalTableStyles.tfoot}>
                <tr>
                  <td>Sıralanır: {ops.count}</td>
                  <td colSpan={7}></td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {ops
        && !isLoading
        &&
        <PaginationComponent totalCount={ops.count} />
      }
    </div>
  )
}

export default OperationTable