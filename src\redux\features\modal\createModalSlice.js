import { createSlice } from "@reduxjs/toolkit";

export const createModalSlice = ({ name, initialData = {} }) => {
  const initialState = {
    isModalOpen: false,
    modalData: initialData
  };

  return createSlice({
    name,
    initialState,
    reducers: {
      setModalData: (state, action) => {
        state.modalData = action.payload;
      },
      handleModal: state => {
        state.isModalOpen = !state.isModalOpen;
      }
    }
  });
};