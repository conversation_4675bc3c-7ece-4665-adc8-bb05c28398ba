import { useRouteError, <PERSON> } from "react-router-dom"

function ErrorPage() {
  const error = useRouteError();

  const isUnauthorized = error?.status === 401 ||
    (error?.data && error?.data?.code === "AUTH_SESSION_EXPIRED");

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full text-center">
        <div className="mb-6">
          <div className="text-red-500 text-7xl mb-4">
            {error?.status || "!"}
          </div>
          <h1 className="text-2xl font-bold text-gray-800 mb-3">
            {error?.status === 404 ? "Səhifə tapılmadı" :
              isUnauthorized ? "Sessiya müddəti bitdi" : "Xəta baş verdi"}
          </h1>
          <div className="text-gray-600 mb-6">
            {error?.status === 404 ? (
              "Axtardığınız səhifə mövcud deyil və ya silinib."
            ) : isUnauthorized ? (
              "Sessiya müddəti bitdi, xahiş edirik yenidən giriş edin."
            ) : (
              <p>{error?.message || "Gözlənilməz bir xəta baş verdi."}</p>
            )}
            {error?.type && !isUnauthorized && (
              <p className="mt-2 text-sm text-gray-500">
                Xəta növü: {error.type}
              </p>
            )}
            {error?.data?.message && !isUnauthorized && (
              <p className="mt-2 text-sm text-gray-500">
                {error.data.message}
              </p>
            )}
          </div>
        </div>

        <div className="space-y-3">
          {isUnauthorized && (
            <Link
              to="/login"
              className="block w-full px-4 py-2 text-white bg-[var(--main-color)] rounded hover:bg-[var(--secondary-color)] transition-colors duration-200"
            >
              Giriş səhifəsinə qayıt
            </Link>
          )}
          <Link
            to="/"
            className="block w-full px-4 py-2 text-white bg-[var(--main-color)] rounded hover:bg-[var(--secondary-color)] transition-colors duration-200"
          >
            Ana səhifəyə qayıt
          </Link>
          <button
            onClick={() => window.location.reload()}
            className="block w-full px-4 py-2 text-[var(--main-color)] border border-[var(--main-color)] rounded hover:bg-gray-50 transition-colors duration-200"
          >
            Səhifəni yenilə
          </button>
        </div>
      </div>
    </div>
  )
}

export default ErrorPage
