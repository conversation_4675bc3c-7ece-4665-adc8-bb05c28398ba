import { Link, Outlet } from "react-router-dom"
import inkredologo from "@/assets/inkredo.svg"

import classes from "./authlayout.module.css";
import ScrollToTop from "@/components/shared/Routing/ScrollToTop.jsx";

function AuthLayout() {
    return (
        <>
            <ScrollToTop />
            <main className={`${classes.mainContainer} flex items-center justify-center`}>
                <div className={`${classes.authWrapper} flex items-center justify-center`}>
                    <div className={`${classes.innerWrapper} flex items-center justify-center flex-col`}>
                        <Link to={"/"}><img src={inkredologo} alt="logo" className="block" /></Link>
                        <Outlet />
                    </div>
                </div>
            </main>
        </>
    )
}

export default AuthLayout