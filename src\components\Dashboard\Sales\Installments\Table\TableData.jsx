import { handleModal, setModalData } from "@/redux/features/modal/contractPaymentSlice";
import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";
import { contactColorsData } from "@/util/constants";
import { useDispatch } from "react-redux";
import { Link } from "react-router-dom";

import swal from "sweetalert";

function TableData({ installment }) {
  const dispatch = useDispatch();

  function handleRowDoubleClick() {
    if (installment?.contract_status == "CANCELLED") {
      swal({
        title: "Müqavilə artıq ləğv edilib",
        icon: "error",
      });
      return;
    }
    else if (installment?.contract_status == "WAITING") {
      swal({
        title: "Gözləmə statusunda olan müqavilədə ödəniş etmək olmaz",
        icon: "error",
      });
      return;
    }

    if (installment.payment_status == "PAID" || installment.payment_status == "OVERPAYMENT") {
      swal({
        title: "Bu kredit artıq ödənib",
        icon: "error",
      });
      return;
    }

    dispatch(handleModal());
    dispatch(setModalData({
      installment: installment.id,
      amount: installment.payment_status == "INCOMPLETE"
        ? (parseFloat(installment.amount) - parseFloat(installment.paid_amount)).toFixed(2)
        : parseFloat(installment.amount),
    }));
  }


  return (
    <tr
      onDoubleClick={handleRowDoubleClick}
      className={`${generalTableStyles[contactColorsData[installment.color]]}`}
    >
      <td>
        <Link className="text-[#094c89] font-[500]" to={`../contracts/${installment.contract_id}`}>
          {installment.contract_unique_id}
        </Link>
      </td>
      <td>{installment?.month_no}</td>
      <td>{installment?.customer?.fullname}</td>
      <td>{installment.product_name}</td>
      <td>{installment.payment_date}</td>
      <td>{installment.amount}</td>
      <td>{installment?.guarantor?.fullname}</td>
      <td>{installment.remaining_debt}</td>
    </tr>

    // {/* <Tooltip title={installment.executor?.fullname} arrow placement="bottom" {...tooltipProps}>
    //   <td className="max-w-[7vw] truncate">{installment.executor?.fullname}</td>
    // </Tooltip> */}
  )
}

export default TableData