import { Dialog, DialogContent, TextField } from "@mui/material";

import { modalStyles } from "@/styles/shared/modalStyles";
import { useDispatch, useSelector } from "react-redux";
import { handleModal } from "@/redux/features/modal/contractPaymentSlice";
import { useEffect, useState } from "react";

import { usePayInstallationMutation } from "@/redux/services/contractApiSlice.js";
import { errorFinder } from "@/util/errorHandler.js";
import { objectFormatter } from "@/util/formatters.js";
import { showToast } from "@/util/showToast.js";
import { formatToDMY } from "@/util/dateHelpers";

export default function PaymentModal({ isOpen = false }) {
  const { modalData: { installment, amount } } = useSelector(state => state.contractPayment);
  const [localAmount, setLocalAmount] = useState(amount);

  useEffect(() => {
    setLocalAmount(amount);
  }, [amount, isOpen]);

  console.log(amount, localAmount);

  const [payInstallation, { isLoading }] = usePayInstallationMutation();
  const dispatch = useDispatch();

  async function handleSubmit(e) {
    e.preventDefault();

    if (localAmount < 1) {
      return;
    }

    try {
      const fd = new FormData(e.target);
      await handlePayment(fd);
      dispatch(handleModal());
    } catch (error) {
      console.log(error);

      const msg = errorFinder(error);

      showToast(msg, "error");
    }
  }

  const handlePayment = async (fd) => {
    const currentDate = fd.get("paid_date");
    const [date, time] = currentDate.split("T");

    const updatedDate = `${formatToDMY(date)} ${time}`;

    const data = {
      amount: localAmount,
      note: fd.get("note"),
      installment: installment,
      paid_date: updatedDate
    };
    console.log(data);

    await payInstallation(objectFormatter(data)).unwrap();
  };

  function getTodayDateTimeLocalString() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
  }

  return (
    <Dialog
      sx={modalStyles.payment.sxstyle}
      slotProps={modalStyles.payment.slotprops}
      open={isOpen}
      onClose={(_, reason) => {
        if (reason === "backdropClick" || reason === "escapeKeyDown") {
          dispatch(handleModal());
        }
      }}
    >
      <DialogContent>
        <form onSubmit={handleSubmit} className="w-full">
          <h2 className={`modalTitle mb-[30px]`}>Cari Ay Ödənişi</h2>
          <div className="flex w-full justify-between mb-[40px] items-center">
            <span className="w-1/3 text-[14px]">Məbləğ</span>
            <TextField
              name="amount"
              className="w-2/3"
              variant="standard"
              value={localAmount || ""}
              type="number"
              onChange={(e) => setLocalAmount(e.target.value)}
              inputProps={{ step: "any" }}
              error={localAmount < 1}
              helperText={localAmount < 1 ? `Minimum məbləğ 1 AZN-dir.` : ""}
            />
          </div>
          <div className="flex w-full justify-between mb-[40px] items-center">
            <span className="w-1/3 text-[14px]">Tarix</span>
            <input defaultValue={getTodayDateTimeLocalString()} className="w-2/3 border-b-2 border-black outline-none text-[14px]" type="datetime-local" name="paid_date" id="paid_date" />
          </div>
          <div className="mb-[40px]">
            <TextField
              id="outlined-multiline-static"
              label="Qeyd"
              name="note"
              multiline
              minRows={4}
              className="w-full"
              variant="outlined"
            />
          </div>
          <div className="flex gap-[28px] justify-end">
            <button type="button" onClick={() => dispatch(handleModal())} className="defButton bgGray">Ləğv et</button>
            <button disabled={isLoading} className="defButton bgGreen">{isLoading ? "Ödəniş Edilir" : "Ödəniş Et"}</button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
