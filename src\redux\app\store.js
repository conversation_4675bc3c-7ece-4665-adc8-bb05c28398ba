import { configureStore } from "@reduxjs/toolkit";
import authReducer from "@/redux/features/authSlice";
import cityReducer from "@/redux/features/citySlice";
import infiniteScrollReducer from "@/redux/features/infiniteScrollSlice";
import layoutReducer from "@/redux/features/layout/layoutSlice";
import balanceModalReducer from "@/redux/features/modal/balanceModalSlice";
import contractPaymentReducer from "@/redux/features/modal/contractPaymentSlice";
import { publicApiSlice } from "@/redux/services/publicApiSlice";
import { userApiSlice } from "@/redux/services/userApiSlice";
import { contractApiSlice } from "@/redux/services/contractApiSlice";
import { accountingApiSlice } from "@/redux/services/accountingApiSlice";
import { statsApiSlice } from "@/redux/services/statsApiSlice";
import portmanatReducer from "@/redux/features/portmanatSlice";
import dynamicMainTitleReducer from "@/redux/features/layout/dynamicMainTitleSlice";

const clearStateMiddleware = (store) => (next) => (action) => {
  if (
    action.type === "auth/logoutSuccess" ||
    action.type === "auth/clearAuth" ||
    action.type === "auth/logoutFailure" ||
    action.type === "auth/logoutStart"
  ) {
    store.dispatch(publicApiSlice.util.resetApiState());
    store.dispatch(userApiSlice.util.resetApiState());
    store.dispatch(contractApiSlice.util.resetApiState());
    store.dispatch(accountingApiSlice.util.resetApiState());
    store.dispatch(statsApiSlice.util.resetApiState());
    
    // store.dispatch({ type: 'city/clearSelectedCity' });
    // store.dispatch({ type: 'infiniteScroll/clearData' });
    // store.dispatch({ type: 'layout/reset' });
    // store.dispatch({ type: 'balanceModal/reset' });
    // store.dispatch({ type: 'contractPayment/reset' });
    // store.dispatch({ type: 'portmanat/reset' });
    // store.dispatch({ type: 'dynamicMainTitle/reset' });
  }
  return next(action);
};

export const store = configureStore({
  reducer: {
    auth: authReducer,
    city: cityReducer,
    layout: layoutReducer,
    infiniteScroll: infiniteScrollReducer,
    balanceModal: balanceModalReducer,
    contractPayment: contractPaymentReducer,
    portmanat: portmanatReducer,
    dynamicMainTitle: dynamicMainTitleReducer,
    [publicApiSlice.reducerPath]: publicApiSlice.reducer,
    [userApiSlice.reducerPath]: userApiSlice.reducer,
    [contractApiSlice.reducerPath]: contractApiSlice.reducer,
    [accountingApiSlice.reducerPath]: accountingApiSlice.reducer,
    [statsApiSlice.reducerPath]: statsApiSlice.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(
      publicApiSlice.middleware,
      userApiSlice.middleware,
      contractApiSlice.middleware,
      accountingApiSlice.middleware,
      statsApiSlice.middleware,
      clearStateMiddleware
    ),
});
