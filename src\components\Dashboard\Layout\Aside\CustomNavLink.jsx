import { useGetUserProfileQuery } from "@/redux/services/userApiSlice";
import { getItemPath, isItemActive } from "@/util/drawerNavConfig";
import { Tooltip } from "@mui/material";
import { NavLink, useLocation } from "react-router-dom";

function CustomNavLink(
    {
        item,
        isCollapsed,
    }
) {
    const { data: user } = useGetUserProfileQuery();

    const location = useLocation();

    const path = getItemPath(item, user);
    const active = isItemActive(item, location);

    return (
        <NavLink
            end
            to={path}
            className={({ isActive: linkIsActive }) =>
                `${linkIsActive || active ? "bg-white text-[#073763]" : "bg-[#073763] text-white"}
flex items-center gap-[27px] rounded-md nav-link-transition whitespace-nowrap`
            }
        >
            {({ isActive: linkIsActive }) => (
                isCollapsed ? (
                    <Tooltip title={item.text} placement="right" arrow>
                        <div className="flex gap-7 py-[15px] px-5 w-full">
                            <img
                                src={(linkIsActive || active) ? item.activeImg : item.nonActiveImg}
                                alt={item.imgAlt || "nav item"}
                                className="mx-auto"
                            />
                        </div>
                    </Tooltip>
                ) : (
                    <div className="flex gap-7 py-[15px] px-5 w-full">
                        <img
                            src={(linkIsActive || active) ? item.activeImg : item.nonActiveImg}
                            alt={item.imgAlt || "nav item"}
                        />
                        <span>{item.text}</span>
                    </div>
                )
            )}
        </NavLink>
    );
}

export default CustomNavLink