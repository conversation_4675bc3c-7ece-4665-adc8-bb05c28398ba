import inputstyleclasses from "@/pages/Auth/inputstyle.module.css"

function PasswordInput({ register = null, errors = {}, disabled = false }) {
    return (
        <p className={`${inputstyleclasses.inputStyle}`}>
            <input
                className="w-full"
                placeholder="Şifrə"
                type="password"
                disabled = {disabled}
                {...(register ? register("password") : {name: "password"})}
            />
            {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.password?.message}</span>}
        </p>
    )
}

export default PasswordInput