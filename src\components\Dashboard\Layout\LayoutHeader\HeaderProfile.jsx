import { useEffect, useState } from "react";
import { Menu, MenuItem } from "@mui/material";
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { logoutFailure, logoutStart, logoutSuccess } from "@/redux/features/authSlice";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useGetUserProfileQuery, useLogoutUserMutation } from "@/redux/services/userApiSlice";
import { showToast } from "@/util/showToast";
import { errorFinder } from "@/util/errorHandler";

export default function HeaderProfile() {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const dispatch = useDispatch();
    const navigate = useNavigate();

    const { data: userProfile, isError: userInfoError } = useGetUserProfileQuery();
    const [logoutUserMutation] = useLogoutUserMutation();

    const refresh = useSelector((state) => state.auth.refresh);

    console.log(userProfile);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const logoutUser = async () => {
        try {
            dispatch(logoutStart());
            await logoutUserMutation({ refresh }).unwrap();
            dispatch(logoutSuccess());
        } catch (error) {
            dispatch(logoutFailure());
            const msg = errorFinder(error);
            showToast(msg, "error");
        }
    };


    useEffect(() => {
        if (userInfoError) {
            const error = new Error();
            error.status = 401;
            error.data = {
                message: "Sessiya müddəti bitdi, xahiş edirik yenidən giriş edin",
                code: "AUTH_SESSION_EXPIRED"
            };
            error.type = "fetchError";
            throw error;
        }
    }, [userInfoError]);

    function navigateToProfile() {
        navigate("/profile");
        handleClose();
    }

    return (
        <div>
            <button
                onClick={handleClick}
                className="blueText !text-[14px] flex items-center gap-1"
            >
                {userProfile?.fullname}
                <KeyboardArrowDownIcon
                    className={`transition-transform ${open ? 'rotate-180' : ''}`}
                    fontSize="small"
                />
            </button>

            <Menu
                id="menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                anchorOrigin={{
                    vertical: "bottom",
                    horizontal: "right",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                }}
            >
                <MenuItem className="!text-[14px]" onClick={navigateToProfile}>İstifadəçi Məlumatları</MenuItem>
                <MenuItem className="!text-[14px]" onClick={logoutUser}>Çıxış Et</MenuItem>
            </Menu>
        </div>
    );
}
