import { useState } from "react";
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from "recharts";
import DateRangePicker from "./DateRangePicker";
import { useGetAllSalesQuery } from "@/redux/services/statsApiSlice";
import { formatDateForAPI } from "@/util/dateHelpers";

const SalesChart = () => {
  const [dateRange, setDateRange] = useState({
    fromDate: "2025-01-01",
    toDate: "2025-12-31"
  });

  // Format dates for API and use correct field names
  const apiParams = {
    start_date: formatDateForAPI(dateRange.fromDate),
    end_date: formatDateForAPI(dateRange.toDate)
  };

  const {data, isLoading, isFetching, isError, error } = useGetAllSalesQuery(apiParams);

  // Transform backend data for bar chart - group by month and sum values
  const transformDataForChart = (backendData) => {
    if (!backendData) return [];

    const monthNames = ["<PERSON>", "Fev", "Mar", "Apr", "May", "<PERSON><PERSON>", "<PERSON>yl", "Avq", "Sen", "Okt", "Noy", "Dek"];
    const monthlyData = {};

    // Group data by month and sum values
    Object.keys(backendData).forEach(date => {
      const dateObj = new Date(date.split('-').reverse().join('-')); // Convert DD-MM-YYYY to YYYY-MM-DD
      const monthYear = `${dateObj.getFullYear()}-${dateObj.getMonth()}`; // Create unique month-year key
      const monthName = monthNames[dateObj.getMonth()];

      if (!monthlyData[monthYear]) {
        monthlyData[monthYear] = {
          name: monthName,
          value: 0,
          year: dateObj.getFullYear(),
          month: dateObj.getMonth()
        };
      }

      monthlyData[monthYear].value += backendData[date];
    });

    // Convert to array and sort by date
    return Object.values(monthlyData).sort((a, b) => {
      if (a.year !== b.year) return a.year - b.year;
      return a.month - b.month;
    }).map(item => ({
      name: item.name,
      value: item.value
    }));
  };

  const chartData = transformDataForChart(data);

  // Calculate total from actual data
  const total = data ? Object.values(data).reduce((sum, value) => sum + (value || 0), 0) : 0;

  // Format large numbers for Y-axis display
  const formatYAxisValue = (value) => {
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(0)}K`;
    }
    return value.toString();
  };

  const handleFromDateChange = (newDate) => {
    setDateRange(prev => ({ ...prev, fromDate: newDate }));
  };

  const handleToDateChange = (newDate) => {
    setDateRange(prev => ({ ...prev, toDate: newDate }));
  };

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Satışlar</h2>
        <DateRangePicker 
          fromDate={dateRange.fromDate}
          toDate={dateRange.toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
        />
      </div>
      <div style={{ height: 180 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 20, right: 20, bottom: 5, left: 0 }}>
            <CartesianGrid strokeDasharray="3 3" horizontal={true} vertical={false} />
            <XAxis dataKey="name" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} tickFormatter={formatYAxisValue} />
            <Tooltip formatter={(value) => [`₼${value.toLocaleString()}`, 'Satış']} />
            <Bar dataKey="value" barSize={20}>
              {chartData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill="#007bff"
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
      <div className="mt-4 text-center">
        <div className="text-2xl font-bold">₼{total.toLocaleString()}</div>
        <div className="text-sm text-gray-500">Ümumi Satış</div>
      </div>
    </div>
  );
};

export default SalesChart;
