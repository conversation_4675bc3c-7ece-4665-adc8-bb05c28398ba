.usersWrapper {
    padding: 24px 24px 24px 0;
    width: calc(100% - 70px);
    transition: width 0.3s ease;
}

.searchOpened {
    width: calc(100% - 290px);
}

.usersGrid {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(230px, 1fr));
    gap: 24px;
}

@media screen and (min-width: 1448px) {
    .usersGrid {
        grid-template-columns: repeat(4, 1fr)
    }
}