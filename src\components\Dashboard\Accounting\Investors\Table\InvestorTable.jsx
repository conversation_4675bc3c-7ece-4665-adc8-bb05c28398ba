import { useState } from "react";
import { useSearchParams } from "react-router-dom";

import IncomeExpenseModal from "../ActionModal/IncomeExpenseModal.jsx";
import PortmanatTransferModal from "../ActionModal/PortmanatTransferModal.jsx";
import TableData from "./TableData.jsx";
import TableHeader from "./TableHeader.jsx";
import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";
import investorTableClasses from "./investortable.module.css";
import { useSelector, useDispatch } from "react-redux";
import { useGetAllInvestorsQuery } from "@/redux/services/accountingApiSlice.js";
import PaginationComponent from "@/components/shared/Pagination/PaginationComponent.jsx";
import { roundTo2 } from "@/util/formatters.js";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { clearData } from "@/redux/features/infiniteScrollSlice";
import PortmanatPreviewModal from "../ActionModal/PortmanatPreview/PortmanatPreviewModal.jsx";

function InvestorTable() {
  const [searchParams] = useSearchParams();
  const dispatch = useDispatch();

  const limit = searchParams.get("limit") || "16";
  const offset = searchParams.get("offset") || "0";

  const queryObject = {
    limit,
    offset,
    ...Object.fromEntries(searchParams.entries()),
  };

  const { data: investors, isLoading, isError, error } = useGetAllInvestorsQuery(queryObject);

  const [modalState, setModalState] = useState({
    isOpen: false,
    type: null,
    userId: null
  });

  function modalHandler(type = null, userId = null) {
    if (!type) {
      dispatch(clearData());
    }

    setModalState({
      isOpen: type ? true : false,
      type: type,
      userId: userId
    });
  }

  const { isSearchOpen } = useSelector(state => state.layout);
  const { userId } = modalState;

  if (isError) return (
    <p className="errorText">Bir xəta baş verdi xaiş edirik bir daha cəhd edin. Error: {error?.originalStatus || error?.status}</p>
  )

  return (
    <div className="pb-8 pt-[24px] w-full">
      <div className="mb-[24px] gap-[28px] flex flex-col items-start md:flex-row md:items-center">
        <button onClick={() => modalHandler("investor")} className="defButton bgGreen">
          İnvestisiya əlavə et
        </button>
        <button onClick={() => modalHandler("expense")} className="defButton bgGreen">Pul çəkimi</button>
        <button onClick={() => modalHandler("transfer")} className="defButton bgGreen">Transfer</button>
      </div>

      {modalState.isOpen && (modalState.type === "investor" || modalState.type === "expense") && (
        <IncomeExpenseModal
          isOpen={modalState.isOpen}
          modalHandler={modalHandler}
          modalType={modalState.type}
          investors={investors?.results || []}
        />
      )}

      {modalState.isOpen && modalState.type === "transfer" && (
        <PortmanatTransferModal
          isOpen={modalState.isOpen}
          modalHandler={modalHandler}
          investors={investors?.results || []}
        />
      )}

      {modalState.isOpen && modalState.type === "portmanatPreview" && (
        <PortmanatPreviewModal
          isOpen={modalState.isOpen}
          modalHandler={modalHandler}
          userId={userId}
        />
      )}

      <div
        className={`${generalTableStyles.tableWrapper} ${investorTableClasses.tableWrapper} ${isSearchOpen && generalTableStyles.searchOpened}`}
      >
        {isLoading ? (
          <ContentLoadingScreen />
        ) : (
          investors
            && investors.results
            && investors.results.data
            && investors.results.data.length > 0
            ? <table className={generalTableStyles.table}>
              <TableHeader />
              <tbody className={generalTableStyles.tbody}>
                {investors &&
                  investors.results.data.map((investor) => (
                    <TableData key={investor.id} investor={investor} modalHandler={modalHandler} />
                  ))}
              </tbody>
              <tfoot className={generalTableStyles.tfoot}>
                <tr>
                  <td>Sıralanır: {investors.count}</td>
                  <td colSpan={3}></td>
                  <td className="text-white">{roundTo2(investors.results.extra.total_previous_balance)}</td>
                  <td colSpan={2}></td>
                  <td className="text-white">{roundTo2(investors.results.extra.total_final_balance)}</td>
                </tr>
              </tfoot>
            </table>
            : <p className="text-center my-[40px]">Məlumat tapılmadı</p>
        )}
      </div>
      {investors
        && !isLoading
        && (
          <PaginationComponent
            totalCount={investors.count}
          />
        )}
    </div>
  );
}

export default InvestorTable;
