import * as yup from "yup";

const emailRegex =
  /(?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|"(?:[\x21\x23-\x5b\x5d-\x7f]|\\[\x21-\x7f])*")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9]))\.){3}(?:(2(5[0-5]|[0-4][0-9])|1[0-9][0-9]|[1-9]?[0-9])|[a-z0-9-]*[a-z0-9]:(?:[\x21-\x5a\x53-\x7f]|\\[\x21-\x7f])+)\])/i;

export const registerSchema = yup.object().shape({
  username: yup
    .string()
    .matches(
      /^[\w.@+-]+$/,
      "İstifadəçi adı yalnız hərflər, rəqəmlər və . @ + - _ simvollarından ibarət ola bilər"
    )
    .max(150, "İstifadəçi adı maksimum 150 simvol ola bilər")
    .required("İstifadəçi adı tələb olunur"),

  email: yup
    .string()
    .max(254, "E-mail maksimum 254 simvol ola bilər")
    .matches(emailRegex, "E-mail düzgün formatda deyil")
    .required("E-mail tələb olunur"),

  password: yup
    .string()
    .min(8, "Şifrədə minimum 8 simvol olmalıdır")
    .max(128, "Şifrə maksimum 128 simvol ola bilər")
    .required("Şifrə tələb olunur"),

  password2: yup
    .string()
    .min(1, "Şifrəni təkrarlayın")
    .max(128, "Şifrə maksimum 128 simvol ola bilər")
    .oneOf([yup.ref("password"), null], "Şifrələr uyğun deyil")
    .required("Şifrəni təkrarlamaq tələb olunur"),

  fullname: yup
    .string()
    .required("Ad, soyad, ata adı, daxil edin")
});

export const loginSchema = yup.object().shape({
  username: yup
    .string()
    .required("İstifadəçi adı tələb olunur"),

  password: yup
    .string()
    .required("Şifrə tələb olunur"),
});