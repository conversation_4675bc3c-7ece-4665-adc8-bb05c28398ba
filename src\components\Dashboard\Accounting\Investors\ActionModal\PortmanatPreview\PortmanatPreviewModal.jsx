import { Dialog, DialogContent } from "@mui/material";
import { modalStyles } from "@/styles/shared/modalStyles.js";
import { useGetPortmanatQuery } from "@/redux/services/accountingApiSlice";
import generalTableStyles from "@/styles/shared/generalTableStyles.module.css";
import TableHeader from "./TableHeader";

export default function PortmanatPreviewModal({ isOpen = false, modalHandler, userId }) {
    const { data, isLoading, isError } = useGetPortmanatQuery(
        { user_id: userId },
        {
            skip: !userId,
            refetchOnMountOrArgChange: true
        }
    );

    const portmanats = data?.results || [];

    const handleClose = () => modalHandler(null);

    return (
        <Dialog
            open={isOpen}
            sx={modalStyles.portmanatPreview.sxstyle}
            slotProps={modalStyles.portmanatPreview.slotprops}
            onClose={handleClose}
        >
            <DialogContent>
                <div>
                    <h2 className="modalTitle mb-6 text-center text-xl font-semibold text-blue-900">Portmanatlar</h2>

                    {isLoading && <p className="text-gray-500">Yüklənir...</p>}

                    {isError && (
                        <p className="text-red-500">
                            Məlumatları yükləmək mümkün olmadı. Zəhmət olmasa bir az sonra yenidən cəhd edin.
                        </p>
                    )}

                    {!isLoading && !isError && (
                        <>
                            {portmanats.length > 0 ? (
                                <div className="space-y-4">
                                    {portmanats.length > 0 ? (
                                        <>
                                            {/* <table>
                                                <tbody className={generalTableStyles.tbody}>
                                                    {investors &&
                                                        investors.results.data.map((investor) => (
                                                            <TableData key={investor.id} investor={investor} modalHandler={modalHandler} />
                                                        ))}
                                                </tbody>
                                            </table> */}
                                            <table className={generalTableStyles.table}>
                                                <TableHeader />
                                                <tbody>
                                                    {portmanats.map((item) => (
                                                        <tr key={item.id}>
                                                            <td className="px-4 py-2 text-gray-900">{item.name}</td>
                                                            <td className="px-4 py-2 text-gray-900">{item.balance} ₼</td>
                                                        </tr>
                                                    ))}
                                                </tbody>
                                            </table>
                                        </>
                                    ) : (
                                        <p className="text-center text-gray-600">Portmanat yoxdur</p>
                                    )}


                                    <div className="flex justify-end pt-4">
                                        <button
                                            type="button"
                                            onClick={handleClose}
                                            className="defButton bgGray"
                                        >
                                            Bağla
                                        </button>
                                    </div>
                                </div>
                            ) : (
                                <p className="text-center text-gray-600">Portmanat yoxdur</p>
                            )}
                        </>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    );
}