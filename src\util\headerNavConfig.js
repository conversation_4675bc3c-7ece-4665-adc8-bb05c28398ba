/*
  /users - all
  /administrators - all
  /contracts - all,
  /investors - ["admin"]
  /ops - all
  /balance - ["admin"]
*/

export const headerNavConfig = [
  {
    path: "/sales",
    routes: [
      {
        name: "<PERSON>st<PERSON>ad<PERSON>çilər",
        to: "/users",
        limit: 16,
        rolesCanLook: true,
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        to: "/administrators",
        limit: 16,
        rolesCanLook: ["admin"],
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        to: "/contracts",
        limit: 17,
        rolesCanLook: true,
      },
      {
        name: "<PERSON>d<PERSON><PERSON><PERSON>zləmə",
        to: "/installments",
        limit: 17,
        rolesCanLook: true,
      },
    ],
  },
  {
    path: "/accounting",
    routes: [
      {
        name: "İnvestorlar",
        to: "/investors",
        limit: 15,
        rolesCanLook: ["admin"],
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Tarixçəsi",
        to: "/ops",
        limit: 17,
        rolesCanLook: true,
      },
      {
        name: "<PERSON><PERSON><PERSON>",
        to: "/balance",
        limit: null,
        rolesCanLook: ["admin"],
      },
    ],
  },
];