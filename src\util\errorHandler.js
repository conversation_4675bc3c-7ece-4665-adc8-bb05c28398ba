export function errorFinder(error) {
  if (
    (error.status && error.status.toString()[0] === "5") ||
    (error.originalStatus && error.originalStatus.toString()[0] === "5")
  ) {
    return `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bir xəta ba<PERSON> ve<PERSON>, x<PERSON><PERSON> edirik biraz sonra bir daha cəhd edin. Error: ${error?.status} ${error?.originalStatus}`;
  }

  let errorMsg = "";
  const errorObj = error.data || error;

  if (typeof errorObj === "object" && errorObj !== null) {
    Object.keys(errorObj).forEach((key) => {
      let errorArr = errorObj[key];

      if (Array.isArray(errorArr) && errorArr.length > 0) {
        errorMsg += (errorMsg ? " " : "") + errorArr[0];
      } else if (typeof errorArr === "string") {
        errorMsg += (errorMsg ? " " : "") + errorArr;
      }
    });
  }

  return errorMsg.trim();
}