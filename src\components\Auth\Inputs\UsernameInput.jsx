import inputstyleclasses from "@/pages/Auth/inputstyle.module.css"

function UsernameInput({ register = null, errors = {}, disabled = false }) {
  return (
    <p className={`${inputstyleclasses.inputStyle}`}>
      <input
        className="w-full"
        placeholder="İstifadəçi adı"
        type="text"
        disabled = {disabled}
        {...(register ? register("username") : {name: "username"})}
      />
      {register && <span className="text-red-600 h-[23px] block text-[13px] font-[600]">{errors.username?.message}</span>}
    </p>
  )
}

export default UsernameInput