export function generateRows(formData, setFormData, userProfile) {
    const rows = [
        { label: '<PERSON>ə<PERSON><PERSON> adı*', key: 'product_name', color: '#094c89', type: 'text' },
        { label: 'Miq<PERSON><PERSON>*', key: 'product_quantity', color: '#094c89' },
        { label: '<PERSON><PERSON><PERSON><PERSON><PERSON> qiyməti (₼)*', key: 'product_price', color: '#094c89' },
        { label: '<PERSON><PERSON><PERSON> (Ay)*', key: 'loan_term', color: '#094c89' },
        { label: '<PERSON><PERSON><PERSON> (₼)', key: 'initial_payment', color: '#094c89' },
        { label: 'Sərmayə (₼)*', key: 'investment', color: '#094c89', disabled: true, value: formData.investment, bold: true },
        {
            label: 'Min. Aylıq <PERSON> (₼)',
            key: 'monthly_payment',
            color: '#CC1212',
            onBlur: e => {
                const value = Number(e.target.value);
                if (value < formData.minMonthlyPayment) {
                    setFormData((prev) => ({
                        ...prev,
                        monthly_payment: formData.minMonthlyPayment,
                    }));
                }
            },
            inputProps: { min: Number(formData.minMonthlyPayment).toFixed(4), step: 0.0001 }
        },
        { label: 'Komissyon (₼)', key: 'commission', color: '#094c89' },
        { label: 'İllik mənfəət (%)', key: null, color: '#094c89', disabled: true, value: userProfile?.company?.annual_interest_rate },
        { label: 'Xərc Məbləği', key: 'cost_amount', color: '#094c89' },
        { label: 'Yekun məbləğ (₼)', key: null, color: '#094c89', disabled: true, value: formData.totalCost },
    ];

    return rows
}