import { useEffect, useState } from "react"
import { useLocation } from "react-router-dom";
import { mainTitleConfig } from "@/util/mainTitleConfig";
import { useSelector } from "react-redux";

const initialData = "";

function useDynamicTitle() {
    const dynamicData = useSelector((state) => state.dynamicMainTitle);
    const [activeTitle, setActiveTitle] = useState(initialData);

    const routeLocation = useLocation();

    useEffect(() => {
        const foundPathObj = mainTitleConfig.find((item) => {
            const regex = new RegExp(item.path);
            return regex.test(routeLocation.pathname);
        }) || initialData;

        const title = foundPathObj === initialData ? initialData : foundPathObj.title + (foundPathObj.isDynamic && foundPathObj.hasExternalData ? ` / ${dynamicData}` : "");

        setActiveTitle(title);
    }, [dynamicData, routeLocation.pathname]);

    return activeTitle;
}

export default useDynamicTitle;
