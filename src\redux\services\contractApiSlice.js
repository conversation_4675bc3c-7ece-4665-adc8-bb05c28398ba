import { createApi } from "@reduxjs/toolkit/query/react";
import { authBaseQueryWithReauth } from "./baseQueryWithReauth";

export const contractApiSlice = createApi({
  baseQuery: authBaseQueryWithReauth,
  reducerPath: "contractApiSlice",
  tagTypes: ["Contracts", "SingleContract", "Installments"],
  endpoints: (builder) => ({
    addContract: builder.mutation({
      query: (credentials) => ({
        url: `/contracts/create/`,
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Contracts"],
    }),
    getAllContracts: builder.query({
      query: (params) => ({
        url: "/contracts/",
        method: "GET",
        params,
      }),
      providesTags: ["Contracts"],
      keepUnusedDataFor: 1,
    }),
    getSingleContract: builder.query({
      query: (id) => ({
        url: `/contracts/${id}/`,
        method: "GET",
      }),
      providesTags: ["SingleContract"],
      keepUnusedDataFor: 1,
    }),
    updateContractStatus: builder.mutation({
      query: (data) => ({
        url: `/contracts/${data.id}/`,
        method: "PUT",
        body: data.updateStatusData,
      }),
      invalidatesTags: ["SingleContract"],
    }),
    updateContractInfo: builder.mutation({
      query: (data) => ({
        url: `/contracts/${data.id}/`,
        method: "PUT",
        body: data.updateStatusData,
      }),
      invalidatesTags: ["SingleContract"],
    }),
    getContractInstallment: builder.query({
      query: (params) => ({
        url: `/contracts/installments/`,
        method: "GET",
        params: params,
      }),
      providesTags: ["Installments"],
      keepUnusedDataFor: 1,
    }),
    updateInstallment: builder.mutation({
      query: (data) => ({
        url: `/contracts/installments/${data.id}/`,
        method: "PUT",
        body: { paid_amount: data.paid_amount },
      }),
      invalidatesTags: ["Installments"],
    }),
    payInstallation: builder.mutation({
      query: (credentials) => ({
        url: "/contracts/pay-installment/",
        method: "POST",
        body: credentials,
      }),
      invalidatesTags: ["Installments"],
    }),
    cancelContract: builder.mutation({
      query: (id) => ({
        url: `/contracts/${id}/`,
        method: "DELETE",
      }),
      invalidatesTags: ["SingleContract"],
    }),
    exportContract: builder.mutation({
      queryFn: async (credentials, api) => {
        try {
          const state = api.getState();
          const token = state.auth.access;

          const response = await fetch(
            `${
              import.meta.env.VITE_API_URL
            }/contracts/export-contracts-to-excel/`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
              },
              body: JSON.stringify(credentials),
            }
          );

          if (!response.ok) {
            return {
              error: {
                status: response.status,
                data: await response.text(),
              },
            };
          }

          const blob = await response.blob();

          let fileName = `contract-${credentials.contract_id || "export"}.xlsx`;
          const contentDisposition = response.headers.get(
            "Content-Disposition"
          );
          if (contentDisposition && contentDisposition.includes("filename=")) {
            const fileNameMatch =
              contentDisposition.match(/filename="?([^"]+)"?/);
            if (fileNameMatch && fileNameMatch[1]) {
              fileName = fileNameMatch[1];
            }
          }

          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = fileName;
          document.body.appendChild(a);
          a.click();

          setTimeout(() => {
            URL.revokeObjectURL(url);
            document.body.removeChild(a);
          }, 200);

          return { data: { success: true } };
        } catch (error) {
          return {
            error: {
              status: "FETCH_ERROR",
              data: error.message,
            },
          };
        }
      },
    }),
    getAllInstallments: builder.query({
      query: (params) => ({
        url: "/contracts/installments/",
        method: "GET",
        params,
      }),
      keepUnusedDataFor: 1,
      providesTags: ["Installments"],
    }),
  }),
});

export const {
  useAddContractMutation,
  useGetAllContractsQuery,
  useGetSingleContractQuery,
  useUpdateContractStatusMutation,
  useUpdateContractInfoMutation,
  useGetContractInstallmentQuery,
  usePayInstallationMutation,
  useCancelContractMutation,
  useExportContractMutation,
  useUpdateInstallmentMutation,
  useGetAllInstallmentsQuery,
} = contractApiSlice;
