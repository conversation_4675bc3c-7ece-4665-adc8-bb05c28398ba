import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  items: [],
  loading: false,
  error: null
};

const portmanatSlice = createSlice({
  name: "portmanat",
  initialState,
  reducers: {
    addPortmanatLocal: (state, action) => {
      state.items.push(action.payload);
    },
    setLoading: (state, action) => {
      state.loading = action.payload;
    },
    setError: (state, action) => {
      state.error = action.payload;
    },
    clearPortmanats: (state) => {
      state.items = [];
      state.error = null;
    },
    reset: () => initialState
  }
});

export const { addPortmanatLocal, clearPortmanats, setLoading, setError, reset } = portmanatSlice.actions;
export default portmanatSlice.reducer;
