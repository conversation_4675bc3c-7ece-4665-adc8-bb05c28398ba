import swal from "sweetalert";

export function verifyMove() {
  return new Promise((resolve) => {
    swal({
      title: "Məlumat",
      text: "Mü<PERSON><PERSON>əyə keçməmişdən əvvəl istifadəçi məlumatlarını əlavə etmisinizsə (düzəliş etmisinizsə) yaddaşa verməyi unutmayın. Əks təqdirdə yenidən daxil etməli olaca<PERSON>sınız, müqaviləyə keçilsin?",
      icon: "warning",
      buttons: {
        cancel: {
          text: "Yadda saxlamadan keç",
          value: false,
          visible: true,
          className: "btn-cancel",
        },
        confirm: {
          text: "Yadda saxla və keç",
          value: true,
          visible: true,
          className: "btn-confirm",
        },
      },
      dangerMode: true,
      closeOnClickOutside: false,
      closeOnEsc: false,
    }).then((value) => {
      if (value === true) {
        console.log("User clicked 'Yadda saxla və keç'");
        resolve(true);
      } else if (value === false) {
        console.log("User clicked 'Yadda saxlamadan keç'");
        resolve(false);
      }
    });

    const escHandler = (event) => {
      if (event.key === "Escape") {
        console.log("User pressed ESC");
        resolve(null);
        swal.close();
        document.removeEventListener("keydown", escHandler);
      }
    };

    document.addEventListener("keydown", escHandler, { once: true });

    setTimeout(() => {
      const overlay = document.querySelector(".swal-overlay");
      const modal = document.querySelector(".swal-modal");

      if (overlay) {
        overlay.addEventListener(
          "click",
          (event) => {
            if (modal && !modal.contains(event.target)) {
              console.log("User clicked outside (backdrop)");
              resolve(null);
              swal.close();
            }
          },
          { once: true }
        );
      }
    }, 100);
  });
}