import { contractDetailsColumns } from "./contractDetailsColumns";

export const routeRoleAccessConfig = {
  users: {
    roles: ["admin", "manager"],
    admin: {
      //what can admin do
    },
  },
  administrators: {
    roles: ["admin"],
    admin: {
      //what can admin do
    },
  },  
  userAddEdit: {
    roles: ["admin", "manager"],
    admin: {
      canAddOrEditEmail: true,
      canAddOrSeePortmanat: true,
      canAddUserAsEmployee: true
    },
    manager: {
      canAddOrEditEmail: false,
      canAddOrSeePortmanat: false,
      canAddUserAsEmployee: false
    },
  },
  contracts: {
    roles: ["admin", "manager"],
    admin: {
      //what can admin do
    },
  },
  contractDetails: {
    roles: ["admin", "manager"],
    columns: contractDetailsColumns,
  },
  investors: {
    roles: ["admin"],
    admin: {
      //what can admin do
    },
  },
  ops: {
    roles: ["admin", "manager"],
    admin: {
      //what can admin do
    },
  },
  balance: {
    roles: ["admin"],
    admin: {
      //what can admin do
    },
  },
  profile: {
    roles: ["admin", "manager"],
    admin: {
      //what can admin do
    },
  },
};
