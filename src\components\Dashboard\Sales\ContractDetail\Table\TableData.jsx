import generalTableStyles from "@/styles/shared/generalTableStyles.module.css"
import { useDispatch } from "react-redux";
import { handleModal, setModalData } from "@/redux/features/modal/contractPaymentSlice";

import swal from "sweetalert"
import { Tooltip } from "@mui/material";
import { contactColorsData } from "@/util/constants";

function TableData({ installment, status, installmentArray, columns, contractUIState, setContractUIState }) {
    const dispatch = useDispatch();

    function handleRowDoubleClick() {
        const firstToBePaidCredit = installmentArray.find(
            (item) =>
                item.payment_status == "INCOMPLETE" || item.payment_status == "UNPAID"
        )?.id;

        if (status == "CANCELLED") {
            swal({
                title: "Müqavilə artıq ləğv edilib",
                icon: "error",
            });
            return;
        }
        else if (status == "WAITING") {
            swal({
                title: "<PERSON>ö<PERSON>ləmə statusunda olan müqavilədə ödəniş etmək olmaz",
                icon: "error",
            });
            return;
        }

        if (installment.id > firstToBePaidCredit) {
            swal({
                title: `İlk öncə ${firstToBePaidCredit} ID-li krediti ödəyin`,
                icon: "error",
            });
            return;
        }

        if (installment.payment_status == "PAID" || installment.payment_status == "OVERPAYMENT") {
            swal({
                title: "Bu kredit artıq ödənib",
                icon: "error",
            });
            return;
        }

        dispatch(handleModal());
        dispatch(setModalData({
            installment: installment.id,
            amount: installment.payment_status == "INCOMPLETE"
                ? (parseFloat(installment.amount) - parseFloat(installment.paid_amount)).toFixed(2)
                : parseFloat(installment.amount),
        }));
    }

    function handleContextMenu(e) {
        e.preventDefault();
        installment.payment_status != "UNPAID"
            && setContractUIState({
                isContextMenuOpen: true,
                top: e.pageY,
                left: e.pageX,
                contract: {
                    id: installment.id,
                    paid_amount: installment.paid_amount
                }
            });
    }

    onclick = () => {
        contractUIState.isContextMenuOpen && setContractUIState({
            ...contractUIState,
            isContextMenuOpen: false,
            top: 0,
            left: 0,
        });
    }

    return (
        <>
            <tr
                onContextMenu={handleContextMenu}
                onDoubleClick={handleRowDoubleClick}
                className={`${generalTableStyles[contactColorsData[installment.color]]}`}
            >
                {columns.map(column => {
                    if (column.hasTooltip) {
                        return (
                            <td key={column.accessor}>
                                <Tooltip title={installment[column.accessor]} arrow placement="bottom">
                                    <div className={generalTableStyles.noteTextStyle}>
                                        {installment[column.accessor]}
                                    </div>
                                </Tooltip>
                            </td>
                        )
                    }
                    if (column.renderCell) {
                        return (
                            <td className="whitespace-nowrap" key={column.accessor}>
                                {column.renderCell(installment)}
                            </td>
                        );
                    }
                    return (
                        <td className="whitespace-nowrap" key={column.accessor}>
                            {installment.is_active && column.isNumber ? 0 : installment[column.accessor]}
                        </td>
                    );
                })}
            </tr>
        </>
    )
}

export default TableData
