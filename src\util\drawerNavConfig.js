import homepagelogo from "../assets/homepagelogo.svg";
import homepagelogoactive from "../assets/homepagelogoactive.svg";
import accountinglogo from "../assets/accountinglogo.svg";
import accountinglogoactive from "../assets/accountinglogoactive.svg";
import saleslogo from "../assets/saleslogo.svg";
import saleslogoactive from "../assets/saleslogoactive.svg";

export const drawerNavConfig = [
  {
    text: "Ana Səhifə",
    path: "/",
    nonActiveImg: homepagelogo,
    activeImg: homepagelogoactive,
    hasRoleBasedControl: false,
  },
  {
    text: "Satışlar",
    limit: 16,
    offset: 0,
    path: "/sales/users",
    nonActiveImg: saleslogo,
    activeImg: saleslogoactive,
    imgAlt: "sales logo",
    pathCheckText: "sales",
    hasRoleBasedControl: false,
  },
  {
    text: "Mühasibat",
    offset: 0,
    hasRoleBasedControl: true,
    roles: [
      {
        role: "admin",
        viewPath: "/accounting/investors",
        limit: 15,
      },
      {
        role: "manager",
        viewPath: "/accounting/ops",
        limit: 17,
      },
    ],
    nonActiveImg: accountinglogo,
    activeImg: accountinglogoactive,
    imgAlt: "accounting logo",
    pathCheckText: "accounting",
  },
];

export const getItemPath = (item, user) => {
  if (item.hasRoleBasedControl && user) {
    const roleMatch = item.roles.find(
      (roleItem) => roleItem.role === user.role
    );
    if (roleMatch) {
      let queryParams = "";
      if (roleMatch.limit !== undefined) {
        queryParams = `?limit=${roleMatch.limit}&offset=0`;
      }
      return `${roleMatch.viewPath}${queryParams}`;
    }
    return "#";
  }

  if (item.limit !== undefined && item.offset !== undefined) {
    return `${item.path}?limit=${item.limit}&offset=${item.offset}`;
  }

  return item.path;
};

export const isItemActive = (item, location) => {
  if (item.pathCheckText) {
    return location.pathname.includes(item.pathCheckText);
  }
  return location.pathname === item.path;
};
