function EditInstallmentBtn({ contractUIState, openModal }) {
    return (
        <div
            style={{
                position: "absolute",
                top: contractUIState.top,
                left: contractUIState.left,
                backgroundColor: "white",
                border: "1px solid var(--table-border-color)",
                borderRadius: "8px",
                boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
                zIndex: 1000,
            }}
        >
            <button
                className="px-4 py-2 w-full text-left hover:bg-gray-100 rounded transition-colors"
                onClick={() => openModal()}>
                Düzəliş et
            </button>
        </div>
    )
}

export default EditInstallmentBtn