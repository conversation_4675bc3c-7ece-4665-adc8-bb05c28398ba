import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  selectedCity: null,
};

const citySlice = createSlice({
  name: "city",
  initialState,
  reducers: {
    setSelectedCity: (state, action) => {
      state.selectedCity = action.payload;
    },
    clearSelectedCity: (state) => {
      state.selectedCity = null;
    },
  },
});

export const { setSelectedCity, clearSelectedCity } = citySlice.actions;
export default citySlice.reducer;
