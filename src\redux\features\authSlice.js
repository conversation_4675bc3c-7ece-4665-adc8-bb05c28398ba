import { createSlice } from "@reduxjs/toolkit";

const getStorageItem = (key) => localStorage.getItem(key);
const setStorageItem = (key, value) => localStorage.setItem(key, value);
const removeStorageItem = (key) => localStorage.removeItem(key);

const initialState = {
  access: getStorageItem("access") || null,
  refresh: getStorageItem("refresh") || null,
  logoutLoading: false,
};

function clearAuthHelper(state) {
  state.access = null;
  state.refresh = null;
  removeStorageItem("access");
  removeStorageItem("refresh");
}

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    setCredentials: (state, action) => {
      const { access, refresh } = action.payload;
      state.access = access;
      state.refresh = refresh;
      setStorageItem("access", access);
      setStorageItem("refresh", refresh);
    },
    clearAuth: (state) => {
      clearAuthHelper(state);
    },
    logoutStart(state) {
      state.logoutLoading = true;
    },
    logoutSuccess(state) {
      state.logoutLoading = false;
      clearAuthHelper(state);
    },
    logoutFailure(state) {
      state.logoutLoading = false;
      clearAuthHelper(state);
    },
  },
});

export const {
  setCredentials,
  logoutStart,
  clearAuth,
  logoutSuccess,
  logoutFailure,
} = authSlice.actions;
export default authSlice.reducer;
