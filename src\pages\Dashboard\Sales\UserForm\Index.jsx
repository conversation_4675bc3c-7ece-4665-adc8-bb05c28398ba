import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { useParams } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";

import { useAddUserMutation, useGetSingleUsersQuery, useGetUserProfileQuery, useUpdateUserMutation } from "@/redux/services/userApiSlice";

import { userFormSchema as schema } from "@/util/schemas/userFormSchema";
import { Helmet } from "react-helmet-async";
import { useEffect, useState } from "react";
import PortmanatModal from "@/components/Dashboard/Sales/UserForm/ActionModal";
import PersonalDetailsForm from "@/components/Dashboard/Sales/UserForm/PersonalDetailsForm";
import FinancialAndAdditionalForm from "@/components/Dashboard/Sales/UserForm/FinancialAndAdditionalForm";
import { setDynamicMainTitle } from "@/redux/features/layout/dynamicMainTitleSlice";
import ContentLoadingScreen from "@/components/shared/LoadingScreen/ContentLoadingScreen";
import { useUserFormSubmit } from "@/hooks/useUserFormSubmit";
import { routeRoleAccessConfig } from "@/util/route-role-access-config/route-role-access-config";

function UserFormPage() {
  const { id } = useParams();
  const { data: user, isLoading: getSingleUserLoading, error, isError, refetch } = useGetSingleUsersQuery(id, { skip: !id });

  const [addUser, { isLoading: addUserLoading }] = useAddUserMutation();
  const [updateUser, { isLoading: updateUserLoading }] = useUpdateUserMutation();

  const [portmanatModalOpen, setPortmanatModalOpen] = useState(false);

  const selectedCity = useSelector((state) => state.city.selectedCity);
  const dispatch = useDispatch();

  const isInvestorBalance = !id;

  const { data: userProfile } = useGetUserProfileQuery();

  const methods = useForm({
    mode: "onBlur",
    resolver: yupResolver(schema),
    context: { isInvestorBalance, isAdmin: userProfile?.role === "admin" }
  });

  const { handleSubmit, trigger, formState, getValues, reset } = methods;

  const { isDirty } = formState;

  const { handleSubmit: onSubmit } = useUserFormSubmit({
    id,
    user,
    selectedCity,
    addUser,
    updateUser,
    reset,
    getValues,
    trigger,
    isDirty
  });

  useEffect(() => {
    dispatch(setDynamicMainTitle(getSingleUserLoading ? "Yüklənilir..." : user?.fullname));
  }, [user?.fullname, dispatch, getSingleUserLoading]);

  const canAddPortmanat = routeRoleAccessConfig.userAddEdit?.[userProfile.role].canAddOrSeePortmanat;

  return (
    <>
      <Helmet>
        <title>
          inKredo | {getSingleUserLoading ? "Yüklənir..." : id ? `İstifadəçilər | ${id}` : "İstifadəçi Əlavə et"}
        </title>
      </Helmet>
      <div className="mt-5 h-full w-full pr-7">
        <FormProvider {...methods}>
          <form className="flex gap-[188px] formElement" onSubmit={(e) => e.preventDefault()}>
            {isError && error?.status === 404 ? (
              <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center text-xl font-semibold">
                Məlumat Tapılmadı
              </div>
            ) : id && getSingleUserLoading ? (
              <div className="w-full h-[calc(100vh-200px)] flex justify-center items-center">
                <ContentLoadingScreen />
              </div>
            ) : (
              <>
                <PersonalDetailsForm user={user} userFormConfig={routeRoleAccessConfig.userAddEdit} userProfile={userProfile} />
                <FinancialAndAdditionalForm user={user} userFormConfig={routeRoleAccessConfig.userAddEdit} userProfile={userProfile} />
              </>
            )}
            <PortmanatModal
              isOpen={portmanatModalOpen}
              modalHandler={() => setPortmanatModalOpen(false)}
              refetch={refetch}
            />
            <div className="fixed bottom-8 right-10 flex items-center gap-[12px]">
              <button
                type="button"
                onClick={handleSubmit((data) => onSubmit(data, "save"))}
                disabled={addUserLoading || updateUserLoading}
                className="defButton bgGreen"
              >
                {addUserLoading || updateUserLoading ? "Yadda saxlanılır..." : "Yadda saxla"}
              </button>

              {id && canAddPortmanat
                && <button
                  type="button"
                  onClick={() => setPortmanatModalOpen(true)}
                  className="defButton bgSecondary"
                  disabled={addUserLoading || updateUserLoading}
                >
                  Portmanat əlavə et
                </button>
              }

              <button
                type="button"
                onClick={() => onSubmit(getValues(), "contract")}
                className="defButton bgSecondary"
                disabled={addUserLoading || updateUserLoading}
              >
                Müqaviləyə keç
              </button>
            </div>
          </form>
        </FormProvider>
      </div>
    </>
  );
}

export default UserFormPage;
