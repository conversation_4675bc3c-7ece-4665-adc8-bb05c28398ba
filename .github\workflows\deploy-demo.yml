name: Deploy to VPS (Demo Environment)

on:
  push:
    branches: [demo]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 22

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Create .env.demo from Secrets
        run: |
          echo "VITE_API_URL=${{ secrets.DEMO_VITE_API_URL }}" > .env.demo

      - name: Build Vite App (Demo Mode)
        run: npm run build -- --mode=demo

      - name: Deploy to Demo VPS via SSH (Password Auth)
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DEMO_VPS_HOST }}
          username: ${{ secrets.DEMO_VPS_USER }}
          password: ${{ secrets.DEMO_VPS_PASSWORD }}
          port: ${{ secrets.DEMO_VPS_PORT }}
          source: "dist/**"
          target: "/home/<USER>/public_html"
          strip_components: 1