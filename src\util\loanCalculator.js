import { roundTo2 } from './formatters';

function calculatePmt(principal, annualRate, years) {
  const rate = annualRate / 12 / 100;
  const periods = years * 12;

  const pmt =
    (principal * rate * Math.pow(1 + rate, periods)) /
    (Math.pow(1 + rate, periods) - 1);

  return roundTo2(pmt);
}

export function calculateLoanData({
  product_quantity,
  product_price,
  initial_payment,
  cost_amount,
  loan_term,
  annualInterestRate,
}) {
  if (product_quantity < 1) {
    throw new Error("Məhsul sayı minimum 1 ədəd olmalıdır.");
  }
  if (loan_term < 2 || loan_term > 24) {
    throw new Error("Kredit müddəti 2 ilə 24 ay arasında olmalıdır.");
  }

  const totalPrice = product_quantity * product_price - initial_payment;
  const totalInvestment = totalPrice + cost_amount;
  const monthlyInterestRate = annualInterestRate / 12;
  const minPayment = calculatePmt(totalPrice, annualInterestRate, loan_term / 12);
  const finalMinMonthlyPayment = roundTo2(minPayment);
  const totalCost = roundTo2(finalMinMonthlyPayment * loan_term);

  return {
    totalInvestment: roundTo2(totalInvestment),
    annualInterestRate: roundTo2(annualInterestRate),
    monthlyInterestRate: roundTo2(monthlyInterestRate),
    minMonthlyPayment: finalMinMonthlyPayment,
    totalCost,
  };
}