import { createSlice } from "@reduxjs/toolkit";

const userTemplate = {
  fullname: "",
  isListOpen: false,
  selectedValue: null,
  error: false,
};

const initialState = {
  customer: { ...userTemplate },
  guarantor: { ...userTemplate },
  attorney: { ...userTemplate },
  executor: { ...userTemplate },
  investor: { ...userTemplate },
  expense: { ...userTemplate },
};

const infiniteScroll = createSlice({
  name: "infiniteScroll",
  initialState,
  reducers: {
    setData: (state, action) => {
      const { mainKey, key, value } = action.payload;
      if (state[mainKey]) {
        state[mainKey][key] = value;
      }
    },
    clearData: (state) => {
      Object.assign(state, initialState);
    },
    clearSelectedValues: (state, action) => {
      const keys = action.payload;
      keys.forEach((key) => (state[key].selectedValue = null));
    },
  },
});

export const { setData, clearData, clearSelectedValues } = infiniteScroll.actions;
export default infiniteScroll.reducer;
