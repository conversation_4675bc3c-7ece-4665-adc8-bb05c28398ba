import { createRoot } from 'react-dom/client'
import { createBrowser<PERSON>outer, RouterProvider } from 'react-router-dom'
import { Provider } from 'react-redux'
import { HelmetProvider } from 'react-helmet-async'

import "@/styles/index.css"
import "@/styles/App.css"

import { store } from '@/redux/app/store.js'
import ErrorPage from '@/pages/ErrorPage.jsx'
import Login from '@/pages/Auth/Login/Login.jsx'
import Register from '@/pages/Auth/Register/Register.jsx'
import ProtectedRoute from '@/components/shared/Routing/ProtectedRoute.jsx'
import Layout from '@/components/Dashboard/Layout/Layout.jsx'
import Homepage from '@/pages/Dashboard/Home/Homepage.jsx'
import UserProfile from '@/pages/UserProfile/Index.jsx'
import InvestorsPage from '@/pages/Dashboard/Accounting/Investors/Index.jsx'
import OperationHistoryPage from '@/pages/Dashboard/Accounting/OperationHistory/Index.jsx'
import BalancePage from '@/pages/Dashboard/Accounting/Balance/Index.jsx'
import AuthLayout from '@/components/Auth/AuthLayout/AuthLayout.jsx'
import UsersPage from '@/pages/Dashboard/Sales/Users/<USER>'
import UserFormPage from '@/pages/Dashboard/Sales/UserForm/Index.jsx'
import AddContractPage from '@/pages/Dashboard/Sales/AddContract/Index.jsx'
import ContractsPage from '@/pages/Dashboard/Sales/Contracts/Index.jsx'
import ContractDetailPage from '@/pages/Dashboard/Sales/ContractDetail/Index.jsx'
import withRoleAccess from '@/components/shared/Routing/withRoleAccess.jsx';
import InstallmentsPage from './pages/Dashboard/Sales/Installments/Index'
import AdministratorsPage from './pages/Dashboard/Sales/Administrators/Index'
import AdministratorForm from './pages/Dashboard/Sales/AdministratorForm/Index'

const ProtectedBalancePage = withRoleAccess(BalancePage, 'balance');
const ProtectedInvestorsPage = withRoleAccess(InvestorsPage, 'investors');
const ProtectedAdministratorsPage = withRoleAccess(AdministratorsPage, 'administrators');
const ProtectedAdministratorFormPage = withRoleAccess(AdministratorForm, 'administrators');

const router = createBrowserRouter([
  {
    path: "/",
    element: <ProtectedRoute><Layout /></ProtectedRoute>,
    errorElement: <ErrorPage />,
    children: [
      {
        index: true,
        element: <Homepage />,
      },
      {
        path: "/profile",
        element: <UserProfile />
      },
      {
        path: "sales",
        children: [
          {
            path: "users",
            element: <UsersPage />,
          },
          {
            path: "users/:id",
            element: <UserFormPage />,
          },
          {
            path: "users/:customerParamId/contract",
            element: <AddContractPage />,
          },
          {
            path: "users/new/contract",
            element: <AddContractPage />,
          },
          {
            path: "users/new",
            element: <UserFormPage />,
          },
          {
            path: "administrators",
            element: <ProtectedAdministratorsPage />,
          },
          {
            path: "administrators/new",
            element: <ProtectedAdministratorFormPage />,
          },
          {
            path: "administrators/:id",
            element: <ProtectedAdministratorFormPage />,
          },
          {
            path: "contracts",
            element: <ContractsPage />,
          },
          {
            path: "contracts/:id",
            element: <ContractDetailPage />,
          },
          {
            path: "installments",
            element: <InstallmentsPage />,
          },
        ],
      },
      {
        path: "accounting",
        children: [
          {
            path: "investors",
            element: <ProtectedInvestorsPage />
          },
          {
            path: "ops",
            element: <OperationHistoryPage />
          },
          {
            path: "balance",
            element: <ProtectedBalancePage />
          },
        ]
      },
    ],
  },
  {
    element: <AuthLayout />,
    children: [
      {
        path: "/login",
        element: <Login />
      },
      {
        path: "/register",
        element: <Register />
      },
    ]
  }
], {
  future: {
    v7_fetcherPersist: true,
    v7_relativeSplatPath: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true,
  },
});

createRoot(document.getElementById('root')).render(
  <Provider store={store}>
    <HelmetProvider>
      <RouterProvider future={{
        v7_startTransition: true,
      }} router={router} />
    </HelmetProvider>
  </Provider>
)
