import { Pagination, styled } from "@mui/material";

export const StyledPagination = styled(Pagination)(() => ({
  "& .MuiPaginationItem-root": {
    color: "#07376359",
    fontWeight: 500,
    borderRadius: "8px",
    border: "1px solid #00000033",
    minWidth: "42px",
    height: "42px",
    fontSize: "16px",
    margin: "0 4px",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
  "& .MuiPaginationItem-page.Mui-selected": {
    backgroundColor: "#336491",
    color: "white",
    "&:hover": {
      backgroundColor: "#094C89",
    },
  },
  "& .Mui-disabled": {
    backgroundColor: "#0000001A",
    border: "1px solid #00000040",
  },
  "& .MuiPaginationItem-previousNext, & .MuiPaginationItem-firstLast": {
    color: "#07376359",
  },
  "& .MuiPaginationItem-page:hover": {
    backgroundColor: "rgba(9, 76, 137, 0.1)",
  },
}));