import { useSearchParams } from "react-router-dom";
import { StyledPagination } from "./styledPagionation";

function PaginationComponent({ totalCount }) {
  const [searchParams, setSearchParams] = useSearchParams();

  const limit = parseInt(searchParams.get("limit") || "16", 10);
  const offset = parseInt(searchParams.get("offset") || "0", 10);

  const currentPage = Math.floor(offset / limit) + 1;
  const totalPages = Math.max(1, Math.ceil(totalCount / limit));

  const handleChange = (event, newPage) => {
    const newOffset = (newPage - 1) * limit;
    const params = Object.fromEntries(searchParams.entries());
    params.offset = newOffset;
    setSearchParams(params);
  };

  return (
    <div className="w-full flex justify-start mt-4">
      <StyledPagination
        page={currentPage}
        count={totalPages}
        onChange={handleChange}
        variant="outlined"
        shape="rounded"
        showFirstButton
        showLastButton
        siblingCount={1}
      />
    </div>
  );
}

export default PaginationComponent;
