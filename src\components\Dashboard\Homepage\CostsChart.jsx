import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";
import DateRangePicker from "./DateRangePicker";
import { useGetAllCostsQuery } from "@/redux/services/statsApiSlice";
import { formatDateForAPI } from "@/util/dateHelpers";

const CostsChart = () => {
  const [dateRange, setDateRange] = useState({
    fromDate: "2025-01-01",
    toDate: "2025-12-31"
  });
  const COLORS = ["#007bff", "#e9ecef"];

  // Format dates for API and use correct field names
  const apiParams = {
    start_date: formatDateForAPI(dateRange.fromDate),
    end_date: formatDateForAPI(dateRange.toDate)
  };

  const {data, isLoading, isFetching, isError, error } = useGetAllCostsQuery(apiParams);

  // Transform backend data for pie chart
  const chartData = data ? [{ name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", value: data.total_cost }] : [];
  const total = data?.total_cost || 0;

  const handleFromDateChange = (newDate) => {
    setDateRange(prev => ({ ...prev, fromDate: newDate }));
  };

  const handleToDateChange = (newDate) => {
    setDateRange(prev => ({ ...prev, toDate: newDate }));
  };

  return (
    <div className="p-4 rounded-lg shadow bg-white">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Xərclər</h2>
        <DateRangePicker 
          fromDate={dateRange.fromDate}
          toDate={dateRange.toDate}
          onFromDateChange={handleFromDateChange}
          onToDateChange={handleToDateChange}
        />
      </div>
      <div className="flex justify-center items-center" style={{ height: 180 }}>
        <div className="relative" style={{ width: 180, height: 180 }}>
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={chartData}
                dataKey="value"
                outerRadius={80}
                innerRadius={65}
                startAngle={90}
                endAngle={-390}
              >
                <Cell fill={COLORS[0]} />
              </Pie>
            </PieChart>
          </ResponsiveContainer>
          <div className="absolute inset-0 flex flex-col justify-center items-center">
            <div className="text-3xl font-bold">₼{total}</div>
            <div className="text-sm text-gray-500">Ümumi</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CostsChart;
