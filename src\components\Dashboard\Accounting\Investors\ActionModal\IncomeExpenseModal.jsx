import { Dialog, DialogContent, TextField } from "@mui/material";
import { modalStyles } from "@/styles/shared/modalStyles.js";
import { useSelector } from "react-redux";
import { useState, useEffect, useRef } from "react";
import { useExpenseBalanceMutation, useGetPortmanatQuery, useIncomeBalanceMutation } from "@/redux/services/accountingApiSlice.js";
import { objectFormatter } from "@/util/formatters.js";
import { getToday } from "@/util/dateHelpers.js";
import { errorFinder } from "@/util/errorHandler.js";
import AutocompleteWrapper from "@/components/shared/Autocomplete/AutocompleteWrapper.jsx";
import PortmanatSelect from "./shared/PortmanatSelect";
import { showToast } from "@/util/showToast.js";

const initialPortmanat = {
    balance: "",
    id: "",
    name: ""
}

export default function IncomeExpenseModal({ isOpen = false, modalHandler, modalType }) {
    const [incomeBalance, { isLoading: incomeLoading }] = useIncomeBalanceMutation();
    const [expenseBalance, { isLoading: expenseLoading }] = useExpenseBalanceMutation();
    const formRef = useRef(null);

    const { selectedValue } = useSelector(state => state.infiniteScroll[modalType]);
    const queryObject = {
        user_id: selectedValue?.id || null
    }
    const { data: userPortmanats } = useGetPortmanatQuery(queryObject, { skip: !selectedValue?.id, refetchOnMountOrArgChange: true });
    const [portmanat, setPortmanat] = useState({
        source: initialPortmanat
    });

    useEffect(() => {
        if (!isOpen && formRef.current) {
            formRef.current.reset();
            setPortmanat({ source: initialPortmanat });
        }
    }, [isOpen]);

    useEffect(() => {
        if (!selectedValue) {
            setPortmanat({ source: initialPortmanat });
        }
    }, [selectedValue]);

    const isInvestorModal = modalType === "investor";

    async function handleSubmit(e) {
        e.preventDefault();
        const formData = Object.fromEntries(new FormData(e.target).entries());
        const formattedData = {
            ...objectFormatter(formData),
            "investor": selectedValue?.id || null,
            "wallet": portmanat.source.id || null,
            [isInvestorModal ? "income_date" : "expense_date"]: getToday(),
            [isInvestorModal ? "income_amount" : "expense_amount"]: Number(formData[isInvestorModal ? "income_amount" : "expense_amount"]),
        };

        try {
            isInvestorModal ? await incomeBalance(formattedData).unwrap() : await expenseBalance(formattedData).unwrap();
            modalHandler(null);
        } catch (error) {
            console.error(error);
            const msg = errorFinder(error);

            showToast(msg, "error");
        }
    }

    return (
        <Dialog
            open={isOpen}
            sx={modalStyles.investors.sxstyle}
            slotProps={modalStyles.investors.slotprops}
            onClose={() => modalHandler(null)}
        >
            <DialogContent>
                <form ref={formRef} onSubmit={handleSubmit} className="w-full">
                    <h2 className={`modalTitle mb-[30px]`}>{modalType === "investor" ? "İnvestisiya Əlavə et" : "Çəkim Əməliyyatı"}</h2>
                    <AutocompleteWrapper
                        type={modalType}
                        label="İnvestor"
                        queryParams={modalType === "expense" ? { is_investor: true } : {}}
                    />
                    <div className="flex items-end w-full justify-between mb-[15px]">
                        <span className="w-1/3 text-[14px]">Portmanat</span>
                        <PortmanatSelect
                            allPortmanats={userPortmanats?.results || []}
                            portmanat={portmanat}
                            setPortmanat={setPortmanat}
                            selectedValue={selectedValue}
                            userPortmanats={userPortmanats}
                            initialPortmanat={initialPortmanat}
                            type="source"
                        />
                    </div>
                    <div className="flex items-end w-full justify-between mb-[15px]">
                        <span className="w-1/3 text-[14px]">Məbləğ</span>
                        <TextField className="w-2/3" variant="standard" name={isInvestorModal ? "income_amount" : "expense_amount"} />
                    </div>
                    {
                        isInvestorModal
                        && <div className="flex items-end w-full justify-between">
                            <span className="w-1/3 text-[14px]">Müddət</span>
                            <TextField
                                className="w-2/3"
                                variant="standard"
                                name="period_in_months"
                                type="number"
                                slotProps={{ input: { min: 1, max: 99 } }}
                                onChange={(e) => {
                                    const value = parseInt(e.target.value, 10);
                                    if (value < 0) e.target.value = 0;
                                    if (value > 99) e.target.value = 99;
                                }}
                            />
                        </div>
                    }
                    <div className="mt-[32px] mb-[45px]">
                        <TextField className="w-full" label="Qeyd" multiline name="note" rows={4} />
                    </div>
                    <div className="flex gap-[28px] justify-end">
                        <button type="button" onClick={() => modalHandler(null)} className="defButton bgGray">Ləğv et</button>
                        <button
                            className="defButton bgGreen"
                            disabled={incomeLoading || expenseLoading || (modalType === "expense" && !portmanat.source.id)}
                        >
                            {isInvestorModal ? "Əlavə et" : "Çək"}
                        </button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
}
